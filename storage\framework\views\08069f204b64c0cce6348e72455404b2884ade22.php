<?php $__env->startSection('js'); ?>
    <script src="<?php echo e(asset('js/lib/jquery.min.js')); ?>"></script>
    <script src="<?php echo e(asset('js/plugins/select2/js/select2.full.min.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('css'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('js/plugins/select2/css/select2.min.css')); ?>">
    <style>
        .actions-menu .dropdown-item {
            padding: 0.25rem 1rem;
            font-size: 0.85rem;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.04);
            cursor: pointer;
        }

        .form-floating-sm .form-control {
            height: calc(1.5em + 0.5rem + 2px);
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        .btn-icon {
            width: 32px;
            height: 32px;
            padding: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .checkbox-select-all {
            margin-top: 0.25rem;
        }

        .table-filters {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .filter-item {
            flex: 1;
            min-width: 120px;
        }

        .btn-filter-toggle {
            min-width: 120px;
        }

        .badge-count {
            position: absolute;
            top: -5px;
            right: -5px;
            font-size: 0.7rem;
        }

        .sticky-actions {
            position: sticky;
            bottom: 0;
            background: white;
            padding: 0.75rem;
            border-top: 1px solid #eee;
            z-index: 100;
            display: flex;
            justify-content: space-between;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
        }

        .filter-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.25rem;
            margin-top: 0.5rem;
        }

        .filter-tag {
            background: #f0f2f5;
            padding: 0.2rem 0.5rem;
            border-radius: 3px;
            font-size: 0.75rem;
            display: flex;
            align-items: center;
        }

        .filter-tag .remove {
            margin-left: 0.25rem;
            cursor: pointer;
            opacity: 0.6;
        }

        .filter-tag .remove:hover {
            opacity: 1;
        }

        /* Pour le mode compact */
        .table-sm td,
        .table-sm th {
            padding: 0.3rem;
        }

        .compact-view .d-sm-table-cell {
            display: none !important;
        }

        .compact-view td:first-child {
            padding-left: 0.75rem;
        }

        /* Améliorations visuelles */
        .avatar {
            width: 32px;
            height: 32px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .sortable:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .table-active {
            background-color: rgba(13, 110, 253, 0.1) !important;
        }

        .card {
            transition: all 0.2s ease;
        }

        .btn-group .btn {
            position: relative;
        }

        .badge-count {
            position: absolute;
            top: -8px;
            right: -8px;
            font-size: 0.65rem;
            min-width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Animations de chargement */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .table-filters {
                flex-direction: column;
            }

            .filter-item {
                min-width: 100%;
                margin-bottom: 0.5rem;
            }

            /* Mobile-first hero section */
            .content-full {
                padding: 1rem;
            }

            .d-flex.gap-2 {
                flex-wrap: wrap;
                gap: 0.5rem !important;
            }

            /* Mobile table improvements */
            .table-responsive {
                border: none;
                box-shadow: none;
            }

            .table {
                font-size: 0.8rem;
            }

            .table td {
                padding: 0.5rem 0.25rem;
                border: none;
                border-bottom: 1px solid #eee;
            }

            /* Mobile actions */
            .sticky-actions {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background: white;
                padding: 1rem;
                border-top: 2px solid #eee;
                box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
                z-index: 1000;
            }

            .sticky-actions .d-flex {
                flex-direction: column;
                gap: 0.5rem;
            }

            .sticky-actions .btn {
                width: 100%;
                justify-content: center;
            }

            /* Mobile modals */
            .modal-dialog {
                margin: 0;
                max-width: 100%;
                height: 100vh;
            }

            .modal-content {
                height: 100%;
                border-radius: 0;
            }

            .modal-body {
                padding: 1rem;
                overflow-y: auto;
            }

            /* Mobile cards */
            .card-body {
                padding: 1rem;
            }

            /* Mobile dropdowns */
            .dropdown-menu {
                position: fixed !important;
                top: auto !important;
                bottom: 60px !important;
                left: 1rem !important;
                right: 1rem !important;
                width: auto !important;
                max-height: 50vh;
                overflow-y: auto;
            }

            /* Touch-friendly buttons */
            .btn {
                min-height: 44px;
                padding: 0.75rem 1rem;
            }

            .btn-sm {
                min-height: 38px;
                padding: 0.5rem 0.75rem;
            }

            /* Mobile search */
            .input-group {
                margin-bottom: 1rem;
            }

            /* Hide less important columns on mobile */
            .d-none.d-sm-table-cell {
                display: none !important;
            }
        }

        @media (max-width: 576px) {
            /* Extra small devices */
            .content {
                padding: 0.5rem;
            }

            .block {
                margin-bottom: 1rem;
                border-radius: 0.5rem;
            }

            .block-content {
                padding: 1rem;
            }

            /* Stack hero elements */
            .d-flex.justify-content-between {
                flex-direction: column;
                align-items: stretch !important;
                gap: 1rem;
            }

            .d-flex.gap-2.align-items-center {
                justify-content: center;
            }

            /* Mobile table as cards */
            .table-responsive {
                display: block;
            }

            .table,
            .table thead,
            .table tbody,
            .table th,
            .table td,
            .table tr {
                display: block;
            }

            .table thead tr {
                position: absolute;
                top: -9999px;
                left: -9999px;
            }

            .table tr {
                background: white;
                border: 1px solid #ddd;
                border-radius: 0.5rem;
                margin-bottom: 1rem;
                padding: 1rem;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .table td {
                border: none;
                position: relative;
                padding: 0.5rem 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .table td:before {
                content: attr(data-label);
                font-weight: bold;
                color: #666;
                flex: 0 0 40%;
            }

            /* Mobile pagination */
            .pagination {
                justify-content: center;
                flex-wrap: wrap;
            }

            .page-link {
                min-height: 44px;
                min-width: 44px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            .card {
                background-color: #2d3748;
                border-color: #4a5568;
            }

            .table {
                color: #e2e8f0;
            }

            .table-light {
                background-color: #4a5568;
                color: #e2e8f0;
            }

            .loading-overlay {
                background: rgba(45, 55, 72, 0.8);
                color: #e2e8f0;
            }
        }

        /* Print styles */
        @media print {
            .sticky-actions,
            .btn,
            .dropdown,
            .loading-overlay {
                display: none !important;
            }

            .table {
                font-size: 0.8rem;
            }

            .d-none.d-sm-table-cell {
                display: table-cell !important;
            }
        }
    </style>
<?php $__env->stopSection(); ?>

<div>
    <!-- Hero avec statistiques -->
    <div class="bg-body-light">
        <div class="content content-full">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <h1 class="h3 fw-bold mb-1">Gestion des Étudiants</h1>
                    <div class="d-flex gap-3 text-muted fs-sm">
                        <span><i class="fa fa-users me-1"></i> <?php echo e($totalEtudiants); ?> étudiants</span>
                        <?php if($totalSupprimés > 0): ?>
                            <span><i class="fa fa-trash me-1"></i> <?php echo e($totalSupprimés); ?> supprimés</span>
                        <?php endif; ?>
                        <?php if($query || $filtreParcours || $filtreNiveau || $filtreAnnee): ?>
                            <span class="badge bg-info">Filtres actifs</span>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="d-flex gap-2 align-items-center">
                    <!-- Boutons de vue -->
                    <div class="btn-group btn-group-sm" role="group">
                        <button class="btn btn-alt-secondary" wire:click="toggleCompactView"
                                title="<?php echo e($compactView ? 'Vue normale' : 'Vue compacte'); ?>">
                            <i class="fa fa-<?php echo e($compactView ? 'expand' : 'compress'); ?>"></i>
                        </button>
                        <button class="btn btn-alt-secondary" wire:click="toggleFilters"
                                title="<?php echo e($showFilters ? 'Masquer filtres' : 'Afficher filtres'); ?>">
                            <i class="fa fa-filter"></i>
                        </button>
                    </div>

                    <button class="btn btn-sm btn-alt-secondary" wire:click="toggleSupprimesMode">
                        <?php if($viewSupprimesMode): ?>
                            <i class="fa fa-list me-1"></i> Liste principale
                        <?php else: ?>
                            <i class="fa fa-trash me-1"></i> Corbeille
                            <?php if($totalSupprimés > 0): ?>
                                <span class="badge bg-danger badge-count"><?php echo e($totalSupprimés); ?></span>
                            <?php endif; ?>
                        <?php endif; ?>
                    </button>

                    <div class="dropdown d-inline-block">
                        <button class="btn btn-sm btn-primary dropdown-toggle" type="button" id="actionDropdown"
                            data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fa fa-plus me-1"></i> Actions
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="actionDropdown">
                            <li><button class="dropdown-item" wire:click="$toggle('showCreateModal')">
                                    <i class="fa fa-user-plus me-1"></i> Nouvel étudiant
                                </button></li>
                            <li><button class="dropdown-item" onclick="window.print()">
                                    <i class="fa fa-print me-1"></i> Imprimer la liste
                                </button></li>
                            <li><button class="dropdown-item" wire:click="exportStudents">
                                    <i class="fa fa-download me-1"></i> Exporter Excel
                                </button></li>
                            <li>
                                <hr class="dropdown-divider">
                            </li>
                            <li><button class="dropdown-item" wire:click="deleteSelected()"
                                    <?php if(count($selectedItems) == 0): ?> disabled <?php endif; ?>>
                                    <i class="fa fa-trash me-1"></i> Supprimer sélectionnés
                                </button></li>
                            <?php if($viewSupprimesMode): ?>
                                <li><button class="dropdown-item" wire:click="restoreSelected()"
                                        <?php if(count($selectedItems) == 0): ?> disabled <?php endif; ?>>
                                        <i class="fa fa-undo me-1"></i> Restaurer sélectionnés
                                    </button></li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END Hero -->

    <!-- Page Content -->
    <div class="content">
        <div class="block block-rounded">
            <div class="block-content block-content-full">
                <!-- Filtres et recherche améliorés -->
                <?php if($showFilters): ?>
                <div class="card border-0 shadow-sm mb-3">
                    <div class="card-body p-3">
                        <div class="row g-3 align-items-center">
                            <!-- Recherche principale -->
                            <div class="col-md-4">
                                <label class="form-label fs-sm fw-medium mb-1">Recherche</label>
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="fa fa-search text-muted"></i>
                                    </span>
                                    <input type="search" wire:model.debounce.300ms="query"
                                           class="form-control border-start-0 ps-0"
                                           placeholder="Nom, prénom ou téléphone...">
                                    <?php if($query): ?>
                                        <button class="btn btn-outline-secondary" wire:click="$set('query', '')"
                                                title="Effacer la recherche">
                                            <i class="fa fa-times"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Filtres -->
                            <div class="col-md-2">
                                <label class="form-label fs-sm fw-medium mb-1">Parcours</label>
                                <select wire:model="filtreParcours" class="form-select form-select-sm">
                                    <option value="">Tous</option>
                                    <?php $__currentLoopData = $parcours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parcour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($parcour->id); ?>"><?php echo e($parcour->sigle); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fs-sm fw-medium mb-1">Niveau</label>
                                <select wire:model="filtreNiveau" class="form-select form-select-sm">
                                    <option value="">Tous</option>
                                    <?php $__currentLoopData = $niveaux; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $niveau): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($niveau->id); ?>"><?php echo e($niveau->nom); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fs-sm fw-medium mb-1">Année</label>
                                <select wire:model="filtreAnnee" class="form-select form-select-sm">
                                    <option value="">Toutes</option>
                                    <?php $__currentLoopData = $annees; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $annee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($annee->id); ?>"><?php echo e($annee->nom); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fs-sm fw-medium mb-1">Affichage</label>
                                <select wire:model="perPage" class="form-select form-select-sm">
                                    <option value="10">10 lignes</option>
                                    <option value="25">25 lignes</option>
                                    <option value="50">50 lignes</option>
                                    <option value="100">100 lignes</option>
                                </select>
                            </div>
                        </div>

                        <!-- Actions rapides -->
                        <?php if($query || $filtreParcours || $filtreNiveau || $filtreAnnee): ?>
                        <div class="d-flex justify-content-between align-items-center mt-3 pt-2 border-top">
                            <div class="d-flex flex-wrap gap-1">
                                <?php if($filtreParcours): ?>
                                    <span class="badge bg-light text-dark">
                                        Parcours: <?php echo e($parcours->firstWhere('id', $filtreParcours)?->sigle); ?>

                                        <button class="btn-close btn-close-sm ms-1" wire:click="$set('filtreParcours', '')"
                                                aria-label="Supprimer ce filtre"></button>
                                    </span>
                                <?php endif; ?>
                                <?php if($filtreNiveau): ?>
                                    <span class="badge bg-light text-dark">
                                        Niveau: <?php echo e($niveaux->firstWhere('id', $filtreNiveau)?->nom); ?>

                                        <button class="btn-close btn-close-sm ms-1" wire:click="$set('filtreNiveau', '')"
                                                aria-label="Supprimer ce filtre"></button>
                                    </span>
                                <?php endif; ?>
                                <?php if($filtreAnnee): ?>
                                    <span class="badge bg-light text-dark">
                                        Année: <?php echo e($annees->firstWhere('id', $filtreAnnee)?->nom); ?>

                                        <button class="btn-close btn-close-sm ms-1" wire:click="$set('filtreAnnee', '')"
                                                aria-label="Supprimer ce filtre"></button>
                                    </span>
                                <?php endif; ?>
                                <?php if($query): ?>
                                    <span class="badge bg-light text-dark">
                                        Recherche: "<?php echo e($query); ?>"
                                        <button class="btn-close btn-close-sm ms-1" wire:click="$set('query', '')"
                                                aria-label="Supprimer ce filtre"></button>
                                    </span>
                                <?php endif; ?>
                            </div>
                            <button class="btn btn-sm btn-outline-secondary" wire:click="clearAllFilters">
                                <i class="fa fa-times me-1"></i> Tout effacer
                            </button>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>



                <!-- Tableau amélioré -->
                <div class="table-responsive position-relative">
                    <?php if($isLoading): ?>
                    <div class="loading-overlay">
                        <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                        <span class="text-muted">Chargement en cours...</span>
                    </div>
                    <?php endif; ?>

                    <table class="table table-hover table-vcenter <?php echo e($compactView ? 'table-sm compact-view' : ''); ?> fs-sm">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 40px;">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox"
                                               wire:model="selectAll"
                                               <?php if(count($selectedItems) == count($etudiants)): ?> checked <?php endif; ?>
                                               title="Sélectionner tout">
                                    </div>
                                </th>
                                <th wire:click="sortBy('nom')" class="sortable" style="cursor: pointer;">
                                    <div class="d-flex align-items-center">
                                        Étudiant
                                        <?php if($sortField === 'nom'): ?>
                                            <i class="fa fa-sort-<?php echo e($sortDirection === 'asc' ? 'up' : 'down'); ?> ms-1 text-primary"></i>
                                        <?php else: ?>
                                            <i class="fa fa-sort ms-1 text-muted"></i>
                                        <?php endif; ?>
                                    </div>
                                </th>
                                <th class="d-none d-sm-table-cell" wire:click="sortBy('parcour_id')" style="cursor: pointer;">
                                    <div class="d-flex align-items-center">
                                        Parcours
                                        <?php if($sortField === 'parcour_id'): ?>
                                            <i class="fa fa-sort-<?php echo e($sortDirection === 'asc' ? 'up' : 'down'); ?> ms-1 text-primary"></i>
                                        <?php else: ?>
                                            <i class="fa fa-sort ms-1 text-muted"></i>
                                        <?php endif; ?>
                                    </div>
                                </th>
                                <th class="d-none d-sm-table-cell" wire:click="sortBy('niveau_id')" style="cursor: pointer;">
                                    <div class="d-flex align-items-center">
                                        Niveau
                                        <?php if($sortField === 'niveau_id'): ?>
                                            <i class="fa fa-sort-<?php echo e($sortDirection === 'asc' ? 'up' : 'down'); ?> ms-1 text-primary"></i>
                                        <?php else: ?>
                                            <i class="fa fa-sort ms-1 text-muted"></i>
                                        <?php endif; ?>
                                    </div>
                                </th>
                                <th class="d-none d-sm-table-cell" wire:click="sortBy('created_at')" style="cursor: pointer;">
                                    <div class="d-flex align-items-center">
                                        <?php echo e($viewSupprimesMode ? 'Supprimé le' : 'Inscrit le'); ?>

                                        <?php if($sortField === 'created_at'): ?>
                                            <i class="fa fa-sort-<?php echo e($sortDirection === 'asc' ? 'up' : 'down'); ?> ms-1 text-primary"></i>
                                        <?php else: ?>
                                            <i class="fa fa-sort ms-1 text-muted"></i>
                                        <?php endif; ?>
                                    </div>
                                </th>
                                <th class="text-center" style="width: 120px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $etudiants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $etu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr wire:key="row-<?php echo e($etu->id); ?>"
                                    class="<?php echo e(in_array($etu->id, $selectedItems) ? 'table-active' : ''); ?>">
                                    <td data-label="Sélection">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox"
                                                value="<?php echo e($etu->id); ?>" wire:model="selectedItems">
                                        </div>
                                    </td>
                                    <td class="fw-semibold" data-label="Étudiant">
                                        <div class="d-flex align-items-center">
                                            <?php if(!$compactView): ?>
                                            <div class="avatar avatar-sm me-2">
                                                <img src="<?php echo e(asset($etu->user->photo ?? 'media/avatars/avatar0.jpg')); ?>"
                                                     alt="Avatar" class="rounded-circle">
                                            </div>
                                            <?php endif; ?>
                                            <div>
                                                <div class="fw-semibold">
                                                    <?php echo e($etu->user->nom); ?> <?php echo e($etu->user->prenom); ?>

                                                    <?php if($etu->user->sexe === 'F'): ?>
                                                        <i class="fa fa-venus text-pink fs-xs ms-1" title="Femme"></i>
                                                    <?php else: ?>
                                                        <i class="fa fa-mars text-blue fs-xs ms-1" title="Homme"></i>
                                                    <?php endif; ?>
                                                </div>
                                                <?php if(!$compactView): ?>
                                                <div class="fs-xs text-muted">
                                                    <i class="fa fa-phone me-1"></i>
                                                    <?php echo e($etu->user->telephone1 ?? 'Aucun téléphone'); ?>

                                                </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="d-none d-sm-table-cell" data-label="Parcours">
                                        <?php if($etu->parcours == null): ?>
                                            <span class="badge bg-warning text-dark">
                                                <i class="fa fa-exclamation-triangle me-1"></i>Non défini
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-primary"><?php echo e($etu->parcours->sigle); ?></span>
                                            <?php if(!$compactView): ?>
                                                <div class="fs-xs text-muted mt-1"><?php echo e($etu->parcours->nom); ?></div>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </td>
                                    <td class="d-none d-sm-table-cell" data-label="Niveau">
                                        <span class="badge bg-info"><?php echo e($etu->niveau->nom); ?></span>
                                    </td>
                                    <td class="d-none d-sm-table-cell" data-label="<?php echo e($viewSupprimesMode ? 'Supprimé le' : 'Inscrit le'); ?>">
                                        <div class="fs-sm">
                                            <?php echo e($viewSupprimesMode ? $etu->deleted_at?->format('d/m/Y') : $etu->created_at->format('d/m/Y')); ?>

                                        </div>
                                        <?php if(!$compactView): ?>
                                        <div class="fs-xs text-muted">
                                            <?php echo e($viewSupprimesMode ? $etu->deleted_at?->diffForHumans() : $etu->created_at->diffForHumans()); ?>

                                        </div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center" data-label="Actions">
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-alt-secondary dropdown-toggle"
                                                type="button" id="dropdownMenu<?php echo e($etu->id); ?>"
                                                data-bs-toggle="dropdown" aria-expanded="false">
                                                Actions
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end actions-menu"
                                                aria-labelledby="dropdownMenu<?php echo e($etu->id); ?>">
                                                <?php if(!$viewSupprimesMode): ?>
                                                    <li>
                                                        <button type="button" class="dropdown-item"
                                                            wire:click="openEditModal(<?php echo e($etu->id); ?>, <?php echo e($etu->user->id); ?>)">
                                                            <i class="fa fa-pencil-alt me-1"></i> Modifier
                                                        </button>
                                                    </li>
                                                    <li>
                                                        <button type="button" class="dropdown-item"
                                                            wire:click="confirmDelete('<?php echo e($etu->user->nom); ?> <?php echo e($etu->user->prenom); ?>', <?php echo e($etu->id); ?>)">
                                                            <i class="fa fa-trash me-1"></i> Supprimer
                                                        </button>
                                                    </li>
                                                    <li>
                                                        <hr class="dropdown-divider">
                                                    </li>
                                                    <li>
                                                        <button class="dropdown-item"
                                                            wire:click="showNotes(<?php echo e($etu->user->id); ?>, <?php echo e($etu->parcours->id); ?>, <?php echo e($etu->niveau->id); ?>, <?php echo e($etu->annee->id); ?>, '<?php echo e($etu->user->nom); ?> <?php echo e($etu->user->prenom); ?>')">
                                                            <i class="fa fa-graduation-cap me-1"></i> Gérer les notes
                                                        </button>
                                                    </li>
                                                    <?php if($etu->parcours): ?>
                                                        <li>
                                                            <button class="dropdown-item"
                                                                wire:click="viewReleve(<?php echo e($etu->user->id); ?>, <?php echo e($etu->parcours->id); ?>, <?php echo e($etu->niveau->id); ?>, <?php echo e($etu->annee->id); ?>)">
                                                                <i class="fa fa-file-alt me-1"></i> Relevé
                                                            </button>
                                                        </li>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <li>
                                                        <button type="button" class="dropdown-item"
                                                            wire:click="restoreEtu(<?php echo e($etu->id); ?>)">
                                                            <i class="fa fa-undo me-1"></i> Restaurer
                                                        </button>
                                                    </li>
                                                    <li>
                                                        <button type="button" class="dropdown-item text-danger"
                                                            wire:click="deleteDefEtu(<?php echo e($etu->id); ?>)"
                                                            onclick="confirm('Suppression définitive. Continuer?') || event.stopImmediatePropagation()">
                                                            <i class="fa fa-times me-1"></i> Supprimer définitivement
                                                        </button>
                                                    </li>
                                                <?php endif; ?>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fa fa-search fa-2x mb-2"></i>
                                            <p>Aucun étudiant trouvé</p>
                                            <?php if($query || $filtreParcours || $filtreNiveau || $filtreAnnee): ?>
                                                <button class="btn btn-sm btn-alt-secondary"
                                                    wire:click="$set('filtreParcours', ''); $set('filtreNiveau', ''); $set('filtreAnnee', ''); $set('query', '');">
                                                    Effacer les filtres
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <?php if(count($etudiants) > 0): ?>
                            <small class="text-muted">Affichage de <?php echo e($etudiants->firstItem()); ?> à
                                <?php echo e($etudiants->lastItem()); ?> sur <?php echo e($etudiants->total()); ?> étudiants</small>
                        <?php endif; ?>
                    </div>
                    <div>
                        <?php echo e($etudiants->links()); ?>

                    </div>
                </div>

                <!-- Actions groupées améliorées -->
                <?php if(count($selectedItems) > 0): ?>
                    <div class="sticky-actions">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-primary me-2"><?php echo e(count($selectedItems)); ?> sélectionné(s)</span>
                            <small class="text-muted">Actions en lot disponibles</small>
                        </div>
                        <div class="d-flex gap-2 align-items-center">
                            <?php if(!$viewSupprimesMode): ?>
                                <!-- Actions pour les étudiants actifs -->
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button"
                                            data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fa fa-edit me-1"></i> Modifier en lot
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><h6 class="dropdown-header">Changer le parcours</h6></li>
                                        <?php $__currentLoopData = $parcours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parcour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li>
                                                <button class="dropdown-item"
                                                        wire:click="bulkUpdateParcours(<?php echo e($parcour->id); ?>)"
                                                        onclick="confirm('Changer le parcours de <?php echo e(count($selectedItems)); ?> étudiant(s) vers <?php echo e($parcour->sigle); ?>?') || event.stopImmediatePropagation()">
                                                    <?php echo e($parcour->sigle); ?> - <?php echo e($parcour->nom); ?>

                                                </button>
                                            </li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><h6 class="dropdown-header">Changer le niveau</h6></li>
                                        <?php $__currentLoopData = $niveaux; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $niveau): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li>
                                                <button class="dropdown-item"
                                                        wire:click="bulkUpdateNiveau(<?php echo e($niveau->id); ?>)"
                                                        onclick="confirm('Changer le niveau de <?php echo e(count($selectedItems)); ?> étudiant(s) vers <?php echo e($niveau->nom); ?>?') || event.stopImmediatePropagation()">
                                                    <?php echo e($niveau->nom); ?>

                                                </button>
                                            </li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>

                                <button type="button" class="btn btn-sm btn-outline-success"
                                        wire:click="exportSelected()"
                                        title="Exporter les étudiants sélectionnés">
                                    <i class="fa fa-download me-1"></i> Exporter
                                </button>

                                <button type="button" class="btn btn-sm btn-danger" wire:click="deleteSelected()"
                                        onclick="confirm('Supprimer les <?php echo e(count($selectedItems)); ?> éléments sélectionnés?') || event.stopImmediatePropagation()">
                                    <i class="fa fa-trash me-1"></i> Supprimer
                                </button>
                            <?php else: ?>
                                <!-- Actions pour les étudiants supprimés -->
                                <button type="button" class="btn btn-sm btn-success" wire:click="restoreSelected()">
                                    <i class="fa fa-undo me-1"></i> Restaurer la sélection
                                </button>

                                <button type="button" class="btn btn-sm btn-danger"
                                        wire:click="forceDeleteSelected()"
                                        onclick="confirm('ATTENTION: Supprimer définitivement les <?php echo e(count($selectedItems)); ?> éléments sélectionnés? Cette action est irréversible!') || event.stopImmediatePropagation()">
                                    <i class="fa fa-times me-1"></i> Supprimer définitivement
                                </button>
                            <?php endif; ?>

                            <button type="button" class="btn btn-sm btn-outline-secondary"
                                    wire:click="$set('selectedItems', [])" title="Annuler la sélection">
                                <i class="fa fa-times"></i>
                            </button>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <!-- END Page Content -->

    <!-- Modals -->
    <!-- Modal Création Étudiant -->
    <?php if($showCreateModal): ?>
    <div class="modal fade show d-block"  tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createEtudiantModalLabel">Ajouter un nouvel étudiant</h5>
                    <button type="button" class="btn-close" wire:click="$toggle('showCreateModal')" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form wire:submit.prevent="addUser">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label" for="nom">Nom <span
                                        class="text-danger">*</span></label>
                                <input type="text" class="form-control <?php $__errorArgs = ['newUser.nom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="nom" wire:model.defer="newUser.nom" placeholder="Nom">
                                <?php $__errorArgs = ['newUser.nom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="prenom">Prénom <span
                                        class="text-danger">*</span></label>
                                <input type="text"
                                    class="form-control <?php $__errorArgs = ['newUser.prenom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="prenom"
                                    wire:model.defer="newUser.prenom" placeholder="Prénom">
                                <?php $__errorArgs = ['newUser.prenom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label" for="sexe">Sexe <span
                                        class="text-danger">*</span></label>
                                <select class="form-select <?php $__errorArgs = ['newUser.sexe'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="sexe"
                                    wire:model.defer="newUser.sexe">
                                    <option value="">-- Sélectionner --</option>
                                    <option value="M">Homme</option>
                                    <option value="F">Femme</option>
                                </select>
                                <?php $__errorArgs = ['newUser.sexe'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="date_naissance">Date de naissance</label>
                                <input type="date"
                                    class="form-control <?php $__errorArgs = ['newUser.date_naissance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="date_naissance" wire:model.defer="newUser.date_naissance">
                                <?php $__errorArgs = ['newUser.date_naissance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label" for="telephone1">Téléphone <span
                                        class="text-danger">*</span></label>
                                <input type="text"
                                    class="form-control <?php $__errorArgs = ['newUser.telephone1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="telephone1" wire:model.defer="newUser.telephone1" placeholder="Téléphone">
                                <?php $__errorArgs = ['newUser.telephone1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="telephone2">Téléphone 2</label>
                                <input type="text" class="form-control" id="telephone2"
                                    wire:model.defer="newUser.telephone2" placeholder="Téléphone 2">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label" for="niveau_id">Niveau <span
                                        class="text-danger">*</span></label>
                                <select class="form-select <?php $__errorArgs = ['newUser.niveau_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="niveau_id" wire:model.defer="newUser.niveau_id">
                                    <option value="">-- Sélectionner --</option>
                                    <?php $__currentLoopData = $niveaux; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $niveau): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($niveau->id); ?>"><?php echo e($niveau->nom); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['newUser.niveau_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="parcours_id">Parcours <span
                                        class="text-danger">*</span></label>
                                <select class="form-select <?php $__errorArgs = ['newUser.parcour_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="parcours_id" wire:model.defer="newUser.parcour_id">
                                    <option value="">-- Sélectionner --</option>
                                    <?php $__currentLoopData = $parcours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parcour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($parcour->id); ?>"><?php echo e($parcour->sigle); ?> -
                                            <?php echo e($parcour->libelle); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['newUser.parcour_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label" for="adresse">Adresse</label>
                            <textarea class="form-control" id="adresse" wire:model.defer="newUser.adresse" rows="2"></textarea>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-alt-secondary"
                                wire:click="$toggle('showCreateModal')">Annuler</button>
                            <button type="submit" class="btn btn-primary">Enregistrer</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
    <?php endif; ?>

    <?php if($showEditModal): ?>
    <!-- Modal Modification Étudiant -->
    <div class="modal fade show d-block" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editEtudiantModalLabel">Modifier l'étudiant</h5>
                    <button type="button" class="btn-close" wire:click="$toggle('showEditModal')" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form wire:submit.prevent="updateUser">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label" for="edit_nom">Nom <span
                                        class="text-danger">*</span></label>
                                <input type="text"
                                    class="form-control <?php $__errorArgs = ['editUser.nom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="edit_nom"
                                    wire:model.defer="editUser.nom">
                                <?php $__errorArgs = ['editUser.nom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="edit_prenom">Prénom <span
                                        class="text-danger">*</span></label>
                                <input type="text"
                                    class="form-control <?php $__errorArgs = ['editUser.prenom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="edit_prenom" wire:model.defer="editUser.prenom">
                                <?php $__errorArgs = ['editUser.prenom'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label" for="edit_sexe">Sexe <span
                                        class="text-danger">*</span></label>
                                <select class="form-select <?php $__errorArgs = ['editUser.sexe'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="edit_sexe" wire:model.defer="editUser.sexe">
                                    <option value="">-- Sélectionner --</option>
                                    <option value="M">Homme</option>
                                    <option value="F">Femme</option>
                                </select>
                                <?php $__errorArgs = ['editUser.sexe'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="edit_telephone">Téléphone <span
                                        class="text-danger">*</span></label>
                                <input type="text"
                                    class="form-control <?php $__errorArgs = ['editUser.telephone1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="edit_telephone" wire:model.defer="editUser.telephone1">
                                <?php $__errorArgs = ['editUser.telephone1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label" for="edit_niveau_id">Niveau <span
                                        class="text-danger">*</span></label>
                                <select class="form-select <?php $__errorArgs = ['editParcours.niveau_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="edit_niveau_id" wire:model.defer="editParcours.niveau_id">
                                    <option value="">-- Sélectionner --</option>
                                    <?php $__currentLoopData = $niveaux; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $niveau): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($niveau->id); ?>"><?php echo e($niveau->nom); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['editParcours.niveau_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="edit_parcour_id">Parcours <span
                                        class="text-danger">*</span></label>
                                <select class="form-select <?php $__errorArgs = ['editParcours.parcour_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="edit_parcour_id" wire:model.defer="editParcours.parcour_id">
                                    <option value="">-- Sélectionner --</option>
                                    <?php $__currentLoopData = $parcours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parcour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($parcour->id); ?>"><?php echo e($parcour->sigle); ?> -
                                            <?php echo e($parcour->nom); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['editParcours.parcour_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-alt-secondary"
                                wire:click="$toggle('showEditModal')">Annuler</button>
                            <button type="submit" class="btn btn-primary">Enregistrer les modifications</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
    <?php endif; ?>

    <?php if($showDeleteModal): ?>
    <!-- Modal Confirmation Suppression -->
    <div class="modal fade show d-block" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteConfirmModalLabel">Confirmation de suppression</h5>
                    <button type="button" class="btn-close" wire:click="$toggle('showDeleteModal')" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Êtes-vous sûr de vouloir supprimer l'étudiant <strong><?php echo e($etuName); ?></strong> ?</p>
                    <p class="text-danger"><small>Cette action peut être annulée ultérieurement.</small></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-alt-secondary" wire:click="$toggle('showDeleteModal')">Annuler</button>
                    <button type="button" class="btn btn-danger" wire:click="deleteUser">Supprimer</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
    <?php endif; ?>

</div>


<script>
    // Notifications améliorées
    window.addEventListener("showSuccessMessage", event => {
        One.helpersOnLoad(['jq-notify']);
        One.helpers('jq-notify', {
            type: 'success',
            icon: 'fa fa-check me-1',
            message: event.detail.message || 'Opération effectuée avec succès!',
            delay: 4000
        });
    });

    window.addEventListener("showErrorMessage", event => {
        One.helpersOnLoad(['jq-notify']);
        One.helpers('jq-notify', {
            type: 'danger',
            icon: 'fa fa-exclamation-triangle me-1',
            message: event.detail.message || 'Une erreur est survenue!',
            delay: 6000
        });
    });

    window.addEventListener("showInfoMessage", event => {
        One.helpersOnLoad(['jq-notify']);
        One.helpers('jq-notify', {
            type: 'info',
            icon: 'fa fa-info-circle me-1',
            message: event.detail.message,
            delay: 5000
        });
    });

    // Gestion des téléchargements
    window.addEventListener("downloadFile", event => {
        const link = document.createElement('a');
        link.href = event.detail.url;
        link.download = '';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });

    // Date picker
    window.addEventListener("helperDatePicker", event => {
        One.helpersOnLoad(['jq-datepicker']);
    });

    // Amélioration de l'UX avec des raccourcis clavier
    document.addEventListener('keydown', function(e) {
        // Ctrl+N pour nouveau étudiant
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            window.livewire.find('<?php echo e($_instance->id); ?>').call('$toggle', 'showCreateModal');
        }

        // Escape pour fermer les modals
        if (e.key === 'Escape') {
            window.livewire.find('<?php echo e($_instance->id); ?>').call('$set', 'showCreateModal', false);
            window.livewire.find('<?php echo e($_instance->id); ?>').call('$set', 'showEditModal', false);
            window.livewire.find('<?php echo e($_instance->id); ?>').call('$set', 'showDeleteModal', false);
        }

        // Ctrl+F pour focus sur la recherche
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            const searchInput = document.querySelector('input[type="search"]');
            if (searchInput) {
                searchInput.focus();
            }
        }
    });

    // Auto-save des préférences utilisateur
    document.addEventListener('DOMContentLoaded', function() {
        // Restaurer les préférences
        const compactView = localStorage.getItem('student_compact_view') === 'true';
        const showFilters = localStorage.getItem('student_show_filters') !== 'false';

        if (compactView !== window.livewire.find('<?php echo e($_instance->id); ?>').compactView) {
            window.livewire.find('<?php echo e($_instance->id); ?>').set('compactView', compactView);
        }
        if (showFilters !== window.livewire.find('<?php echo e($_instance->id); ?>').showFilters) {
            window.livewire.find('<?php echo e($_instance->id); ?>').set('showFilters', showFilters);
        }
    });

    // Sauvegarder les préférences
    Livewire.on('compactViewChanged', (value) => {
        localStorage.setItem('student_compact_view', value);
    });

    Livewire.on('showFiltersChanged', (value) => {
        localStorage.setItem('student_show_filters', value);
    });

    // Amélioration de la sélection multiple
    document.addEventListener('click', function(e) {
        if (e.target.matches('input[type="checkbox"][wire\\:model="selectedItems"]')) {
            const checkboxes = document.querySelectorAll('input[type="checkbox"][wire\\:model="selectedItems"]');
            const selectAllCheckbox = document.querySelector('input[wire\\:model="selectAll"]');

            if (selectAllCheckbox) {
                const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
                selectAllCheckbox.checked = checkedCount === checkboxes.length;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < checkboxes.length;
            }
        }
    });

    // Tooltip pour les actions
    document.addEventListener('DOMContentLoaded', function() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script><?php /**PATH C:\xampp\htdocs\ImsaaProject\resources\views/livewire/deraq/etudiant/index.blade.php ENDPATH**/ ?>