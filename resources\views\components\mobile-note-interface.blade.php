<!-- Interface mobile optimisée pour les notes -->
<div class="mobile-note-interface d-lg-none">
    <!-- Header mobile -->
    <div class="mobile-header sticky-top bg-white shadow-sm">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center py-2">
                <div class="d-flex align-items-center">
                    <button class="btn btn-link p-0 me-2" wire:click="backToList">
                        <i class="fa fa-arrow-left fa-lg"></i>
                    </button>
                    <div>
                        <h6 class="mb-0 fw-bold">{{ $selectedStudentName }}</h6>
                        <small class="text-muted">{{ count($notes) }} notes</small>
                    </div>
                </div>
                <div class="dropdown">
                    <button class="btn btn-outline-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fa fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><button class="dropdown-item" wire:click="showCreateNoteForm">
                            <i class="fa fa-plus me-2"></i>Ajouter une note
                        </button></li>
                        <li><button class="dropdown-item" wire:click="exportNotes">
                            <i class="fa fa-download me-2"></i>Exporter
                        </button></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><button class="dropdown-item" onclick="showImportExport()">
                            <i class="fa fa-exchange-alt me-2"></i>Import/Export
                        </button></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques mobiles -->
    @if(count($notes) > 0)
    <div class="mobile-stats bg-light py-3">
        <div class="container-fluid">
            <div class="row g-2 text-center">
                <div class="col-3">
                    <div class="stat-item">
                        <div class="stat-value text-primary">{{ number_format($this->calculateAverage(), 1) }}</div>
                        <div class="stat-label">Moyenne</div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="stat-item">
                        <div class="stat-value text-success">{{ $this->getBestNote() }}</div>
                        <div class="stat-label">Meilleure</div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="stat-item">
                        <div class="stat-value text-info">{{ $this->getSuccessRate() }}%</div>
                        <div class="stat-label">Réussite</div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="stat-item">
                        <div class="stat-value text-secondary">{{ count($notes) }}</div>
                        <div class="stat-label">Total</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Filtres mobiles -->
    <div class="mobile-filters bg-white border-bottom">
        <div class="container-fluid py-2">
            <!-- Recherche rapide -->
            <div class="mb-2">
                <div class="input-group input-group-sm">
                    <span class="input-group-text"><i class="fa fa-search"></i></span>
                    <input type="search" class="form-control" wire:model.debounce.300ms="searchQuery" 
                           placeholder="Rechercher...">
                </div>
            </div>
            
            <!-- Filtres en chips -->
            <div class="d-flex gap-2 overflow-auto pb-2">
                <button class="btn btn-sm btn-outline-primary flex-shrink-0 {{ empty($selectedSemestreId) ? 'active' : '' }}" 
                        wire:click="$set('selectedSemestreId', '')">
                    Tous
                </button>
                @foreach($semestres as $sem)
                    <button class="btn btn-sm btn-outline-primary flex-shrink-0 {{ $selectedSemestreId == $sem->id ? 'active' : '' }}" 
                            wire:click="$set('selectedSemestreId', {{ $sem->id }})">
                        {{ $sem->nom }}
                    </button>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Liste des notes en cartes mobiles -->
    <div class="mobile-notes-list">
        <div class="container-fluid py-3">
            @forelse($notes as $note)
                <div class="mobile-note-card mb-3" wire:key="mobile-note-{{ $note->id }}">
                    <div class="d-flex">
                        <!-- Note score -->
                        <div class="note-score-mobile me-3">
                            @php
                                $noteClass = '';
                                if($note->valeur >= 16) $noteClass = 'excellent';
                                elseif($note->valeur >= 14) $noteClass = 'good';
                                elseif($note->valeur >= 10) $noteClass = 'average';
                                else $noteClass = 'poor';
                            @endphp
                            <div class="score-circle {{ $noteClass }}">
                                {{ number_format($note->valeur, 1) }}
                            </div>
                        </div>
                        
                        <!-- Contenu de la note -->
                        <div class="flex-grow-1">
                            <div class="d-flex justify-content-between align-items-start mb-1">
                                <h6 class="mb-0 fw-bold">{{ $note->matiere->nom ?? '-' }}</h6>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-link p-0" type="button" data-bs-toggle="dropdown">
                                        <i class="fa fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li><button class="dropdown-item" wire:click="showEditNoteForm({{ $note->id }})">
                                            <i class="fa fa-edit me-2"></i>Modifier
                                        </button></li>
                                        <li><button class="dropdown-item text-danger" wire:click="deleteNote({{ $note->id }})"
                                                onclick="return confirm('Supprimer cette note ?')">
                                            <i class="fa fa-trash me-2"></i>Supprimer
                                        </button></li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="d-flex align-items-center mb-2">
                                <span class="badge bg-info me-2">{{ $note->typeNote->nom ?? '-' }}</span>
                                <small class="text-muted">
                                    <i class="fa fa-calendar me-1"></i>
                                    {{ $note->created_at->format('d/m/Y') }}
                                </small>
                            </div>
                            
                            @if($note->observation)
                                <p class="mb-0 text-muted small">
                                    <i class="fa fa-comment me-1"></i>
                                    {{ Str::limit($note->observation, 80) }}
                                </p>
                            @endif
                        </div>
                    </div>
                </div>
            @empty
                <div class="text-center py-5">
                    <i class="fa fa-clipboard-list fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucune note</h5>
                    <p class="text-muted mb-3">Commencez par ajouter une première note.</p>
                    <button class="btn btn-primary" wire:click="showCreateNoteForm">
                        <i class="fa fa-plus me-1"></i>Ajouter une note
                    </button>
                </div>
            @endforelse
        </div>
    </div>

    <!-- Bouton d'action flottant mobile -->
    <div class="mobile-fab">
        <button class="fab-button" wire:click="showCreateNoteForm">
            <i class="fa fa-plus"></i>
        </button>
    </div>
</div>

<style>
/* Styles pour l'interface mobile */
.mobile-note-interface {
    min-height: 100vh;
    background: #f8f9fa;
}

.mobile-header {
    z-index: 1020;
    border-bottom: 1px solid #dee2e6;
}

.mobile-stats {
    border-bottom: 1px solid #dee2e6;
}

.stat-item {
    padding: 0.5rem;
}

.stat-value {
    font-size: 1.25rem;
    font-weight: 700;
    line-height: 1;
}

.stat-label {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.mobile-filters {
    position: sticky;
    top: 60px;
    z-index: 1010;
}

.mobile-note-card {
    background: white;
    border-radius: 0.75rem;
    padding: 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    transition: transform 0.2s ease;
}

.mobile-note-card:active {
    transform: scale(0.98);
}

.note-score-mobile {
    flex-shrink: 0;
}

.score-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.9rem;
    color: white;
    position: relative;
}

.score-circle.excellent {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.score-circle.good {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.score-circle.average {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.score-circle.poor {
    background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
}

.mobile-fab {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.fab-button {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fab-button:hover, .fab-button:active {
    transform: scale(1.1);
    box-shadow: 0 12px 30px rgba(102, 126, 234, 0.6);
}

/* Animations */
.mobile-note-card {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .mobile-header .container-fluid,
    .mobile-stats .container-fluid,
    .mobile-filters .container-fluid,
    .mobile-notes-list .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .mobile-note-card {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
    }
    
    .score-circle {
        width: 45px;
        height: 45px;
        font-size: 0.8rem;
    }
    
    .fab-button {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .mobile-fab {
        bottom: 15px;
        right: 15px;
    }
}

/* Mode sombre */
@media (prefers-color-scheme: dark) {
    .mobile-note-interface {
        background: #1a1a1a;
    }
    
    .mobile-header,
    .mobile-filters {
        background: #2d2d2d !important;
        border-color: #404040;
    }
    
    .mobile-note-card {
        background: #2d2d2d;
        border-color: #404040;
        color: #e9ecef;
    }
    
    .mobile-stats {
        background: #2d2d2d !important;
        border-color: #404040;
    }
}

/* Gestes tactiles */
.mobile-note-card {
    touch-action: manipulation;
    user-select: none;
}

/* Amélioration de l'accessibilité */
.fab-button:focus {
    outline: 3px solid rgba(102, 126, 234, 0.5);
    outline-offset: 2px;
}

.mobile-note-card:focus-within {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}
</style>

<script>
// Scripts spécifiques à l'interface mobile
document.addEventListener('DOMContentLoaded', function() {
    // Gestion des gestes de swipe pour les cartes de notes
    let startY = 0;
    let startX = 0;
    
    document.addEventListener('touchstart', function(e) {
        if (e.target.closest('.mobile-note-card')) {
            startY = e.touches[0].clientY;
            startX = e.touches[0].clientX;
        }
    });
    
    document.addEventListener('touchend', function(e) {
        if (e.target.closest('.mobile-note-card')) {
            const endY = e.changedTouches[0].clientY;
            const endX = e.changedTouches[0].clientX;
            const diffY = startY - endY;
            const diffX = startX - endX;
            
            // Swipe vers la gauche pour supprimer (plus de 100px)
            if (Math.abs(diffX) > Math.abs(diffY) && diffX > 100) {
                const noteCard = e.target.closest('.mobile-note-card');
                if (noteCard) {
                    noteCard.style.transform = 'translateX(-100%)';
                    noteCard.style.opacity = '0.5';
                    
                    // Demander confirmation
                    setTimeout(() => {
                        if (confirm('Supprimer cette note ?')) {
                            // Extraire l'ID de la note et appeler la méthode de suppression
                            const noteId = noteCard.getAttribute('wire:key').replace('mobile-note-', '');
                            @this.call('deleteNote', noteId);
                        } else {
                            // Restaurer la position
                            noteCard.style.transform = '';
                            noteCard.style.opacity = '';
                        }
                    }, 200);
                }
            }
        }
    });
    
    // Vibration pour le feedback tactile
    function vibrate(pattern = 50) {
        if (navigator.vibrate) {
            navigator.vibrate(pattern);
        }
    }
    
    // Ajouter la vibration aux boutons importants
    document.addEventListener('click', function(e) {
        if (e.target.closest('.fab-button, .btn-danger')) {
            vibrate(50);
        }
    });
    
    // Optimisation du scroll pour les performances
    let ticking = false;
    
    function updateScrollPosition() {
        const scrollTop = window.pageYOffset;
        const header = document.querySelector('.mobile-header');
        
        if (header) {
            if (scrollTop > 100) {
                header.classList.add('shadow-lg');
            } else {
                header.classList.remove('shadow-lg');
            }
        }
        
        ticking = false;
    }
    
    window.addEventListener('scroll', function() {
        if (!ticking) {
            requestAnimationFrame(updateScrollPosition);
            ticking = true;
        }
    });
});

// Fonction pour afficher le modal d'import/export
function showImportExport() {
    const modal = new bootstrap.Modal(document.getElementById('importExportModal'));
    modal.show();
}
</script>
