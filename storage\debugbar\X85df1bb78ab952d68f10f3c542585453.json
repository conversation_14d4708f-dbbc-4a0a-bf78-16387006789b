{"__meta": {"id": "X85df1bb78ab952d68f10f3c542585453", "datetime": "2025-07-08 15:47:54", "utime": 1751978874.697719, "method": "POST", "uri": "/livewire/message/etudiant", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751978872.023623, "end": 1751978874.697865, "duration": 2.6742420196533203, "duration_str": "2.67s", "measures": [{"label": "Booting", "start": 1751978872.023623, "relative_start": 0, "end": 1751978873.818795, "relative_end": 1751978873.818795, "duration": 1.7951719760894775, "duration_str": "1.8s", "params": [], "collector": null}, {"label": "Application", "start": 1751978873.819645, "relative_start": 1.7960219383239746, "end": 1751978874.697875, "relative_end": 1.0013580322265625e-05, "duration": 0.878230094909668, "duration_str": "878ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 27151256, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "livewire.deraq.etudiant.notes (\\resources\\views\\livewire\\deraq\\etudiant\\notes.blade.php)", "param_count": 59, "params": ["parcours", "semestres", "livewireLayout", "errors", "_instance", "showCreateModal", "showEditModal", "showDeleteModal", "showRestoreModal", "viewSupprimesMode", "selectedEtudiantId", "selectedUserId", "etuName", "newUser", "editUser", "editParcours", "query", "filtreParcours", "filtre<PERSON><PERSON>au", "filtreAnnee", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "bulkAction", "totalEtudiants", "totalSupprimés", "selectedItems", "showNotes", "selectedStudentName", "noteUserId", "noteParcourId", "noteMatiereId", "noteTypeId", "noteValeur", "noteObservation", "noteActionMode", "selectedNoteId", "notes", "matieres", "noteTypes", "current_semestre", "current_niveau", "current_annee", "current_parcour", "selectedSemestreId", "viewMode", "searchQuery", "sortBy", "selectedNoteType", "autoSave", "saveStatus", "notesStatistics", "validationErrors", "isValidating", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/etudiant/notes.blade.php&line=0"}, {"name": "components.note-form-modal (\\resources\\views\\components\\note-form-modal.blade.php)", "param_count": 69, "params": ["__env", "app", "errors", "_instance", "parcours", "semestres", "livewireLayout", "showCreateModal", "showEditModal", "showDeleteModal", "showRestoreModal", "viewSupprimesMode", "selectedEtudiantId", "selectedUserId", "etuName", "newUser", "editUser", "editParcours", "query", "filtreParcours", "filtre<PERSON><PERSON>au", "filtreAnnee", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "bulkAction", "totalEtudiants", "totalSupprimés", "selectedItems", "showNotes", "selectedStudentName", "noteUserId", "noteParcourId", "noteMatiereId", "noteTypeId", "noteValeur", "noteObservation", "noteActionMode", "selectedNoteId", "notes", "matieres", "noteTypes", "current_semestre", "current_niveau", "current_annee", "current_parcour", "selectedSemestreId", "viewMode", "searchQuery", "sortBy", "selectedNoteType", "autoSave", "saveStatus", "notesStatistics", "validationErrors", "isValidating", "page", "paginators", "__currentLoopData", "sem", "loop", "type", "__empty_1", "note", "index", "noteClass"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/components/note-form-modal.blade.php&line=0"}, {"name": "components.toast-notifications (\\resources\\views\\components\\toast-notifications.blade.php)", "param_count": 69, "params": ["__env", "app", "errors", "_instance", "parcours", "semestres", "livewireLayout", "showCreateModal", "showEditModal", "showDeleteModal", "showRestoreModal", "viewSupprimesMode", "selectedEtudiantId", "selectedUserId", "etuName", "newUser", "editUser", "editParcours", "query", "filtreParcours", "filtre<PERSON><PERSON>au", "filtreAnnee", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "bulkAction", "totalEtudiants", "totalSupprimés", "selectedItems", "showNotes", "selectedStudentName", "noteUserId", "noteParcourId", "noteMatiereId", "noteTypeId", "noteValeur", "noteObservation", "noteActionMode", "selectedNoteId", "notes", "matieres", "noteTypes", "current_semestre", "current_niveau", "current_annee", "current_parcour", "selectedSemestreId", "viewMode", "searchQuery", "sortBy", "selectedNoteType", "autoSave", "saveStatus", "notesStatistics", "validationErrors", "isValidating", "page", "paginators", "__currentLoopData", "sem", "loop", "type", "__empty_1", "note", "index", "noteClass"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/components/toast-notifications.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 19, "nb_failed_statements": 0, "accumulated_duration": 0.29363, "accumulated_duration_str": "294ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00624, "duration_str": "6.24ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 2.125}, {"sql": "select * from `notes` where `notes`.`id` in (10139, 9979, 9859, 9819, 9699, 8480, 8415, 8302, 4461, 4180, 2677, 1625, 1511, 1397, 1295, 1163, 1106, 885)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 169}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00113, "duration_str": "1.13ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:80", "connection": "imsaaapp", "start_percent": 2.125, "width_percent": 0.385}, {"sql": "select * from `matieres` where `matieres`.`id` in (33, 34, 35, 36, 37, 38, 41, 42, 43, 45, 196, 197, 198, 199, 200, 201, 202, 203) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 175}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php", "line": 13}, {"index": 25, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php", "line": 18}], "duration": 0.0010400000000000001, "duration_str": "1.04ms", "stmt_id": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php:175", "connection": "imsaaapp", "start_percent": 2.51, "width_percent": 0.354}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 175}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php", "line": 13}, {"index": 25, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php", "line": 18}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php:175", "connection": "imsaaapp", "start_percent": 2.864, "width_percent": 0.232}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (1, 2, 3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 169}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:80", "connection": "imsaaapp", "start_percent": 3.096, "width_percent": 0.266}, {"sql": "select * from `semestres` where `semestres`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 3.361, "width_percent": 0.255}, {"sql": "select * from `niveaux` where `niveaux`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 3.617, "width_percent": 0.255}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00076, "duration_str": "760μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 3.872, "width_percent": 0.259}, {"sql": "select * from `parcours` where `parcours`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 4.131, "width_percent": 0.232}, {"sql": "select * from `notes` where `user_id` = 109 and exists (select * from `matieres` where `notes`.`matiere_id` = `matieres`.`id` and exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `type_note_id` = 1 and `notes`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["109", "5", "1", "4", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 677}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 908}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 95}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 28}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 118}], "duration": 0.008140000000000001, "duration_str": "8.14ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:677", "connection": "imsaaapp", "start_percent": 4.363, "width_percent": 2.772}, {"sql": "select * from `matieres` where `matieres`.`id` in (33, 34, 35, 36, 37, 38, 41, 42, 43, 45, 196, 197, 198, 199, 200, 201, 202, 203) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 677}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 908}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 95}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 28}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 118}], "duration": 0.00117, "duration_str": "1.17ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:677", "connection": "imsaaapp", "start_percent": 7.135, "width_percent": 0.398}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 677}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 908}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 95}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 28}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 118}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:677", "connection": "imsaaapp", "start_percent": 7.533, "width_percent": 0.232}, {"sql": "select count(*) as aggregate from `inscription_students` where exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `inscription_students`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.007, "duration_str": "7ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 7.765, "width_percent": 2.384}, {"sql": "select `id`, `user_id`, `parcour_id`, `niveau_id`, `annee_universitaire_id`, `created_at`, `deleted_at` from `inscription_students` where exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `inscription_students`.`deleted_at` is null order by `created_at` desc limit 10 offset 690", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.1605, "duration_str": "161ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 10.149, "width_percent": 54.661}, {"sql": "select `id`, `nom`, `prenom`, `telephone1`, `sexe`, `photo` from `users` where `users`.`id` in (84, 85, 86, 87, 88, 89, 109, 110, 129, 130) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.04124, "duration_str": "41.24ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 64.809, "width_percent": 14.045}, {"sql": "select `id`, `sigle`, `nom` from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.01988, "duration_str": "19.88ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 78.854, "width_percent": 6.77}, {"sql": "select `id`, `nom` from `niveaux` where `niveaux`.`id` in (1) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0173, "duration_str": "17.3ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 85.625, "width_percent": 5.892}, {"sql": "select `id`, `nom` from `annee_universitaires` where `annee_universitaires`.`id` in (4) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00724, "duration_str": "7.24ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 91.517, "width_percent": 2.466}, {"sql": "select `id`, `nom` from `semestres` where `niveau_id` = 1 and `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 204}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.01767, "duration_str": "17.67ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:204", "connection": "imsaaapp", "start_percent": 93.982, "width_percent": 6.018}]}, "models": {"data": {"App\\Models\\InscriptionStudent": 10, "App\\Models\\Parcour": 2, "App\\Models\\AnneeUniversitaire": 2, "App\\Models\\Niveau": 2, "App\\Models\\Semestre": 3, "App\\Models\\TypeNote": 5, "App\\Models\\Matiere": 36, "App\\Models\\Note": 36, "App\\Models\\User": 11}, "count": 107}, "livewire": {"data": {"etudiant #hoJJz2TNBUmyOStN1MHq": "array:5 [\n  \"data\" => array:54 [\n    \"showCreateModal\" => false\n    \"showEditModal\" => false\n    \"showDeleteModal\" => false\n    \"showRestoreModal\" => false\n    \"viewSupprimesMode\" => false\n    \"selectedEtudiantId\" => null\n    \"selectedUserId\" => null\n    \"etuName\" => null\n    \"newUser\" => []\n    \"editUser\" => []\n    \"editParcours\" => []\n    \"query\" => null\n    \"filtreParcours\" => null\n    \"filtreNiveau\" => null\n    \"filtreAnnee\" => null\n    \"perPage\" => 10\n    \"isLoading\" => false\n    \"sortField\" => \"created_at\"\n    \"sortDirection\" => \"desc\"\n    \"showFilters\" => true\n    \"compactView\" => false\n    \"bulkAction\" => \"\"\n    \"totalEtudiants\" => 720\n    \"totalSupprimés\" => 102\n    \"selectedItems\" => []\n    \"showNotes\" => true\n    \"selectedStudentName\" => \"BEHAVANA Gauffit\"\n    \"noteUserId\" => 109\n    \"noteParcourId\" => null\n    \"noteMatiereId\" => null\n    \"noteTypeId\" => null\n    \"noteValeur\" => null\n    \"noteObservation\" => null\n    \"noteActionMode\" => \"list\"\n    \"selectedNoteId\" => null\n    \"notes\" => Illuminate\\Database\\Eloquent\\Collection {#2252\n      #items: array:18 [\n        0 => App\\Models\\Note {#1929\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 10139\n            \"valeur\" => 4.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 200\n            \"history_note_id\" => 677\n            \"created_at\" => \"2023-09-07 16:39:02\"\n            \"updated_at\" => \"2023-09-07 16:39:02\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 10139\n            \"valeur\" => 4.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 200\n            \"history_note_id\" => 677\n            \"created_at\" => \"2023-09-07 16:39:02\"\n            \"updated_at\" => \"2023-09-07 16:39:02\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1988\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 200\n                \"nom\" => \"ITALIEN\"\n                \"code\" => \"ITALI\"\n                \"syllabus\" => null\n                \"user_id\" => 180\n                \"ue_id\" => 162\n                \"created_at\" => \"2023-09-07 13:10:29\"\n                \"updated_at\" => \"2023-09-07 13:10:29\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #original: array:10 [\n                \"id\" => 200\n                \"nom\" => \"ITALIEN\"\n                \"code\" => \"ITALI\"\n                \"syllabus\" => null\n                \"user_id\" => 180\n                \"ue_id\" => 162\n                \"created_at\" => \"2023-09-07 13:10:29\"\n                \"updated_at\" => \"2023-09-07 13:10:29\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1819\n              #connection: \"mysql\"\n              #table: \"type_notes\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:2 [\n                \"id\" => 1\n                \"nom\" => \"Partiel 1\"\n              ]\n              #original: array:2 [\n                \"id\" => 1\n                \"nom\" => \"Partiel 1\"\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n            }\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        1 => App\\Models\\Note {#1928\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 9979\n            \"valeur\" => 7.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 197\n            \"history_note_id\" => 673\n            \"created_at\" => \"2023-09-07 16:38:49\"\n            \"updated_at\" => \"2023-09-07 16:38:49\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 9979\n            \"valeur\" => 7.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 197\n            \"history_note_id\" => 673\n            \"created_at\" => \"2023-09-07 16:38:49\"\n            \"updated_at\" => \"2023-09-07 16:38:49\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1985\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 197\n                \"nom\" => \"GESTION DE PROJET \"\n                \"code\" => \"GESPROJ\"\n                \"syllabus\" => null\n                \"user_id\" => 157\n                \"ue_id\" => 160\n                \"created_at\" => \"2023-09-07 13:07:18\"\n                \"updated_at\" => \"2023-09-07 13:07:18\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #original: array:10 [\n                \"id\" => 197\n                \"nom\" => \"GESTION DE PROJET \"\n                \"code\" => \"GESPROJ\"\n                \"syllabus\" => null\n                \"user_id\" => 157\n                \"ue_id\" => 160\n                \"created_at\" => \"2023-09-07 13:07:18\"\n                \"updated_at\" => \"2023-09-07 13:07:18\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1819}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        2 => App\\Models\\Note {#1927\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 9859\n            \"valeur\" => 9.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 198\n            \"history_note_id\" => 670\n            \"created_at\" => \"2023-09-07 16:38:45\"\n            \"updated_at\" => \"2023-09-07 16:38:45\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 9859\n            \"valeur\" => 9.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 198\n            \"history_note_id\" => 670\n            \"created_at\" => \"2023-09-07 16:38:45\"\n            \"updated_at\" => \"2023-09-07 16:38:45\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1986\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 198\n                \"nom\" => \"TRANSIT ET DOUANE\"\n                \"code\" => \"TRANSDOU\"\n                \"syllabus\" => null\n                \"user_id\" => 158\n                \"ue_id\" => 161\n                \"created_at\" => \"2023-09-07 13:08:36\"\n                \"updated_at\" => \"2023-09-07 13:08:36\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #original: array:10 [\n                \"id\" => 198\n                \"nom\" => \"TRANSIT ET DOUANE\"\n                \"code\" => \"TRANSDOU\"\n                \"syllabus\" => null\n                \"user_id\" => 158\n                \"ue_id\" => 161\n                \"created_at\" => \"2023-09-07 13:08:36\"\n                \"updated_at\" => \"2023-09-07 13:08:36\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1819}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        3 => App\\Models\\Note {#1926\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 9819\n            \"valeur\" => 12.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 199\n            \"history_note_id\" => 669\n            \"created_at\" => \"2023-09-07 16:38:42\"\n            \"updated_at\" => \"2023-09-07 16:38:42\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 9819\n            \"valeur\" => 12.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 199\n            \"history_note_id\" => 669\n            \"created_at\" => \"2023-09-07 16:38:42\"\n            \"updated_at\" => \"2023-09-07 16:38:42\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1987\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 199\n                \"nom\" => \"DROIT  DE TRANSPORT \"\n                \"code\" => \"DRTRANSP\"\n                \"syllabus\" => null\n                \"user_id\" => 154\n                \"ue_id\" => 161\n                \"created_at\" => \"2023-09-07 13:09:16\"\n                \"updated_at\" => \"2023-09-07 13:09:16\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #original: array:10 [\n                \"id\" => 199\n                \"nom\" => \"DROIT  DE TRANSPORT \"\n                \"code\" => \"DRTRANSP\"\n                \"syllabus\" => null\n                \"user_id\" => 154\n                \"ue_id\" => 161\n                \"created_at\" => \"2023-09-07 13:09:16\"\n                \"updated_at\" => \"2023-09-07 13:09:16\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1819}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        4 => App\\Models\\Note {#1925\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 9699\n            \"valeur\" => 10.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 201\n            \"history_note_id\" => 666\n            \"created_at\" => \"2023-09-07 16:38:36\"\n            \"updated_at\" => \"2023-09-07 16:38:36\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 9699\n            \"valeur\" => 10.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 201\n            \"history_note_id\" => 666\n            \"created_at\" => \"2023-09-07 16:38:36\"\n            \"updated_at\" => \"2023-09-07 16:38:36\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1989\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 201\n                \"nom\" => \"MARKETING\"\n                \"code\" => \"MARKT\"\n                \"syllabus\" => null\n                \"user_id\" => 157\n                \"ue_id\" => 162\n                \"created_at\" => \"2023-09-07 13:10:57\"\n                \"updated_at\" => \"2023-09-07 13:10:57\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #original: array:10 [\n                \"id\" => 201\n                \"nom\" => \"MARKETING\"\n                \"code\" => \"MARKT\"\n                \"syllabus\" => null\n                \"user_id\" => 157\n                \"ue_id\" => 162\n                \"created_at\" => \"2023-09-07 13:10:57\"\n                \"updated_at\" => \"2023-09-07 13:10:57\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1819}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        5 => App\\Models\\Note {#1924\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 8480\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 202\n            \"history_note_id\" => 589\n            \"created_at\" => \"2023-09-07 15:25:08\"\n            \"updated_at\" => \"2023-09-07 15:25:08\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 8480\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 202\n            \"history_note_id\" => 589\n            \"created_at\" => \"2023-09-07 15:25:08\"\n            \"updated_at\" => \"2023-09-07 15:25:08\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1990\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 202\n                \"nom\" => \"PROJET TUTORET \"\n                \"code\" => \"PROJTUTO\"\n                \"syllabus\" => null\n                \"user_id\" => 146\n                \"ue_id\" => 163\n                \"created_at\" => \"2023-09-07 13:11:56\"\n                \"updated_at\" => \"2023-09-07 13:11:56\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #original: array:10 [\n                \"id\" => 202\n                \"nom\" => \"PROJET TUTORET \"\n                \"code\" => \"PROJTUTO\"\n                \"syllabus\" => null\n                \"user_id\" => 146\n                \"ue_id\" => 163\n                \"created_at\" => \"2023-09-07 13:11:56\"\n                \"updated_at\" => \"2023-09-07 13:11:56\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1819}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        6 => App\\Models\\Note {#1923\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 8415\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 203\n            \"history_note_id\" => 587\n            \"created_at\" => \"2023-09-07 15:10:52\"\n            \"updated_at\" => \"2023-09-07 15:10:52\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 8415\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 203\n            \"history_note_id\" => 587\n            \"created_at\" => \"2023-09-07 15:10:52\"\n            \"updated_at\" => \"2023-09-07 15:10:52\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1991\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 203\n                \"nom\" => \"VISITE D'ENTREPRISE \"\n                \"code\" => \"VISI\"\n                \"syllabus\" => null\n                \"user_id\" => 147\n                \"ue_id\" => 163\n                \"created_at\" => \"2023-09-07 13:12:34\"\n                \"updated_at\" => \"2023-09-07 13:12:34\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #original: array:10 [\n                \"id\" => 203\n                \"nom\" => \"VISITE D'ENTREPRISE \"\n                \"code\" => \"VISI\"\n                \"syllabus\" => null\n                \"user_id\" => 147\n                \"ue_id\" => 163\n                \"created_at\" => \"2023-09-07 13:12:34\"\n                \"updated_at\" => \"2023-09-07 13:12:34\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1819}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        7 => App\\Models\\Note {#1922\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 8302\n            \"valeur\" => 10.25\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 196\n            \"history_note_id\" => 582\n            \"created_at\" => \"2023-09-07 15:04:26\"\n            \"updated_at\" => \"2023-09-07 15:04:26\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 8302\n            \"valeur\" => 10.25\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 196\n            \"history_note_id\" => 582\n            \"created_at\" => \"2023-09-07 15:04:26\"\n            \"updated_at\" => \"2023-09-07 15:04:26\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1984\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 196\n                \"nom\" => \"COMPTABILITE\"\n                \"code\" => \"COMP\"\n                \"syllabus\" => null\n                \"user_id\" => 155\n                \"ue_id\" => 160\n                \"created_at\" => \"2023-09-07 13:06:35\"\n                \"updated_at\" => \"2023-09-07 13:06:35\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #original: array:10 [\n                \"id\" => 196\n                \"nom\" => \"COMPTABILITE\"\n                \"code\" => \"COMP\"\n                \"syllabus\" => null\n                \"user_id\" => 155\n                \"ue_id\" => 160\n                \"created_at\" => \"2023-09-07 13:06:35\"\n                \"updated_at\" => \"2023-09-07 13:06:35\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1819}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        8 => App\\Models\\Note {#1921\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 4461\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 42\n            \"history_note_id\" => 265\n            \"created_at\" => \"2023-06-14 11:15:39\"\n            \"updated_at\" => \"2023-06-14 11:15:39\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 4461\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 42\n            \"history_note_id\" => 265\n            \"created_at\" => \"2023-06-14 11:15:39\"\n            \"updated_at\" => \"2023-06-14 11:15:39\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1981\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 42\n                \"nom\" => \"VISITE D'ENTREPRISE \"\n                \"code\" => \"VISENT\"\n                \"syllabus\" => null\n                \"user_id\" => 147\n                \"ue_id\" => 93\n                \"created_at\" => \"2023-06-07 16:38:36\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #original: array:10 [\n                \"id\" => 42\n                \"nom\" => \"VISITE D'ENTREPRISE \"\n                \"code\" => \"VISENT\"\n                \"syllabus\" => null\n                \"user_id\" => 147\n                \"ue_id\" => 93\n                \"created_at\" => \"2023-06-07 16:38:36\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1819}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        9 => App\\Models\\Note {#1920\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 4180\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 41\n            \"history_note_id\" => 246\n            \"created_at\" => \"2023-06-14 10:37:15\"\n            \"updated_at\" => \"2023-06-14 10:37:15\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 4180\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 41\n            \"history_note_id\" => 246\n            \"created_at\" => \"2023-06-14 10:37:15\"\n            \"updated_at\" => \"2023-06-14 10:37:15\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1980\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 41\n                \"nom\" => \"INFORMATIQUE BUREAUTIQUE \"\n                \"code\" => \"INFOR\"\n                \"syllabus\" => null\n                \"user_id\" => 145\n                \"ue_id\" => 93\n                \"created_at\" => \"2023-06-07 16:37:48\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #original: array:10 [\n                \"id\" => 41\n                \"nom\" => \"INFORMATIQUE BUREAUTIQUE \"\n                \"code\" => \"INFOR\"\n                \"syllabus\" => null\n                \"user_id\" => 145\n                \"ue_id\" => 93\n                \"created_at\" => \"2023-06-07 16:37:48\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1819}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        10 => App\\Models\\Note {#1919\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 2677\n            \"valeur\" => 17.22\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 34\n            \"history_note_id\" => 146\n            \"created_at\" => \"2023-06-13 07:32:43\"\n            \"updated_at\" => \"2023-06-13 07:32:43\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 2677\n            \"valeur\" => 17.22\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 34\n            \"history_note_id\" => 146\n            \"created_at\" => \"2023-06-13 07:32:43\"\n            \"updated_at\" => \"2023-06-13 07:32:43\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1975\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 34\n                \"nom\" => \"ITALIEN\"\n                \"code\" => \"TDL1ITA\"\n                \"syllabus\" => null\n                \"user_id\" => 180\n                \"ue_id\" => 90\n                \"created_at\" => \"2023-06-07 16:33:42\"\n                \"updated_at\" => \"2024-02-20 15:20:00\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #original: array:10 [\n                \"id\" => 34\n                \"nom\" => \"ITALIEN\"\n                \"code\" => \"TDL1ITA\"\n                \"syllabus\" => null\n                \"user_id\" => 180\n                \"ue_id\" => 90\n                \"created_at\" => \"2023-06-07 16:33:42\"\n                \"updated_at\" => \"2024-02-20 15:20:00\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1819}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        11 => App\\Models\\Note {#1918\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 1625\n            \"valeur\" => 10.5\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 38\n            \"history_note_id\" => 64\n            \"created_at\" => \"2023-06-09 10:40:48\"\n            \"updated_at\" => \"2023-06-09 10:40:48\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 1625\n            \"valeur\" => 10.5\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 38\n            \"history_note_id\" => 64\n            \"created_at\" => \"2023-06-09 10:40:48\"\n            \"updated_at\" => \"2023-06-09 10:40:48\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1979\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 38\n                \"nom\" => \"GÉOGRAPHIE DES ECHANGES \"\n                \"code\" => \"GEOECH\"\n                \"syllabus\" => null\n                \"user_id\" => 159\n                \"ue_id\" => 92\n                \"created_at\" => \"2023-06-07 16:36:45\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #original: array:10 [\n                \"id\" => 38\n                \"nom\" => \"GÉOGRAPHIE DES ECHANGES \"\n                \"code\" => \"GEOECH\"\n                \"syllabus\" => null\n                \"user_id\" => 159\n                \"ue_id\" => 92\n                \"created_at\" => \"2023-06-07 16:36:45\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1819}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        12 => App\\Models\\Note {#1917\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 1511\n            \"valeur\" => 13.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 35\n            \"history_note_id\" => 62\n            \"created_at\" => \"2023-06-09 10:36:51\"\n            \"updated_at\" => \"2023-06-09 10:36:51\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 1511\n            \"valeur\" => 13.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 35\n            \"history_note_id\" => 62\n            \"created_at\" => \"2023-06-09 10:36:51\"\n            \"updated_at\" => \"2023-06-09 10:36:51\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1976\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 35\n                \"nom\" => \"LOGISTIQUE \"\n                \"code\" => \"LOG\"\n                \"syllabus\" => null\n                \"user_id\" => 158\n                \"ue_id\" => 91\n                \"created_at\" => \"2023-06-07 16:34:24\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #original: array:10 [\n                \"id\" => 35\n                \"nom\" => \"LOGISTIQUE \"\n                \"code\" => \"LOG\"\n                \"syllabus\" => null\n                \"user_id\" => 158\n                \"ue_id\" => 91\n                \"created_at\" => \"2023-06-07 16:34:24\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1819}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        13 => App\\Models\\Note {#1916\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 1397\n            \"valeur\" => 17.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 36\n            \"history_note_id\" => 60\n            \"created_at\" => \"2023-06-09 10:27:16\"\n            \"updated_at\" => \"2023-06-09 10:27:16\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 1397\n            \"valeur\" => 17.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 36\n            \"history_note_id\" => 60\n            \"created_at\" => \"2023-06-09 10:27:16\"\n            \"updated_at\" => \"2023-06-09 10:27:16\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1977\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 36\n                \"nom\" => \"ORGANISATION D'ENTREPRISE \"\n                \"code\" => \"ORGAE\"\n                \"syllabus\" => null\n                \"user_id\" => 148\n                \"ue_id\" => 91\n                \"created_at\" => \"2023-06-07 16:35:04\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #original: array:10 [\n                \"id\" => 36\n                \"nom\" => \"ORGANISATION D'ENTREPRISE \"\n                \"code\" => \"ORGAE\"\n                \"syllabus\" => null\n                \"user_id\" => 148\n                \"ue_id\" => 91\n                \"created_at\" => \"2023-06-07 16:35:04\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1819}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        14 => App\\Models\\Note {#1915\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 1295\n            \"valeur\" => 12.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 37\n            \"history_note_id\" => 56\n            \"created_at\" => \"2023-06-09 10:17:34\"\n            \"updated_at\" => \"2023-06-09 10:17:34\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 1295\n            \"valeur\" => 12.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 37\n            \"history_note_id\" => 56\n            \"created_at\" => \"2023-06-09 10:17:34\"\n            \"updated_at\" => \"2023-06-09 10:17:34\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1978\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 37\n                \"nom\" => \"TECHNIQUE DU COMMERCE INTERNATIONAL \"\n                \"code\" => \"TECHNACOMI\"\n                \"syllabus\" => null\n                \"user_id\" => 157\n                \"ue_id\" => 92\n                \"created_at\" => \"2023-06-07 16:36:04\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #original: array:10 [\n                \"id\" => 37\n                \"nom\" => \"TECHNIQUE DU COMMERCE INTERNATIONAL \"\n                \"code\" => \"TECHNACOMI\"\n                \"syllabus\" => null\n                \"user_id\" => 157\n                \"ue_id\" => 92\n                \"created_at\" => \"2023-06-07 16:36:04\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1819}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        15 => App\\Models\\Note {#1914\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 1163\n            \"valeur\" => 11.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 45\n            \"history_note_id\" => 51\n            \"created_at\" => \"2023-06-09 10:03:14\"\n            \"updated_at\" => \"2023-06-09 10:03:14\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 1163\n            \"valeur\" => 11.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 45\n            \"history_note_id\" => 51\n            \"created_at\" => \"2023-06-09 10:03:14\"\n            \"updated_at\" => \"2023-06-09 10:03:14\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1983\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 45\n                \"nom\" => \"MATHÉMATIQUE FINANCIÈRE\"\n                \"code\" => \"MATHFIN\"\n                \"syllabus\" => null\n                \"user_id\" => 155\n                \"ue_id\" => 94\n                \"created_at\" => \"2023-06-07 16:40:18\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #original: array:10 [ …10]\n              #changes: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [ …7]\n              #guarded: array:1 [ …1]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1819}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        16 => App\\Models\\Note {#1913\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 1106\n            \"valeur\" => 0.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 43\n            \"history_note_id\" => 50\n            \"created_at\" => \"2023-06-09 09:59:29\"\n            \"updated_at\" => \"2023-06-09 09:59:29\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 1106\n            \"valeur\" => 0.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 43\n            \"history_note_id\" => 50\n            \"created_at\" => \"2023-06-09 09:59:29\"\n            \"updated_at\" => \"2023-06-09 09:59:29\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1982\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [ …10]\n              #original: array:10 [ …10]\n              #changes: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [ …7]\n              #guarded: array:1 [ …1]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1819}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        17 => App\\Models\\Note {#1710\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 885\n            \"valeur\" => 16.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 33\n            \"history_note_id\" => 45\n            \"created_at\" => \"2023-06-09 07:59:22\"\n            \"updated_at\" => \"2023-06-09 07:59:22\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 885\n            \"valeur\" => 16.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 33\n            \"history_note_id\" => 45\n            \"created_at\" => \"2023-06-09 07:59:22\"\n            \"updated_at\" => \"2023-06-09 07:59:22\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1974\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [ …10]\n              #original: array:10 [ …10]\n              #changes: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [ …7]\n              #guarded: array:1 [ …1]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1819}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"matieres\" => []\n    \"noteTypes\" => Illuminate\\Database\\Eloquent\\Collection {#2221\n      #items: array:3 [\n        0 => App\\Models\\TypeNote {#1809\n          #connection: \"mysql\"\n          #table: \"type_notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [\n            \"id\" => 1\n            \"nom\" => \"Partiel 1\"\n          ]\n          #original: array:2 [\n            \"id\" => 1\n            \"nom\" => \"Partiel 1\"\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        1 => App\\Models\\TypeNote {#1712\n          #connection: \"mysql\"\n          #table: \"type_notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [\n            \"id\" => 2\n            \"nom\" => \"Partiel 2\"\n          ]\n          #original: array:2 [\n            \"id\" => 2\n            \"nom\" => \"Partiel 2\"\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        2 => App\\Models\\TypeNote {#1845\n          #connection: \"mysql\"\n          #table: \"type_notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [\n            \"id\" => 3\n            \"nom\" => \"Examen\"\n          ]\n          #original: array:2 [\n            \"id\" => 3\n            \"nom\" => \"Examen\"\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"current_semestre\" => App\\Models\\Semestre {#1849\n      #connection: \"mysql\"\n      #table: \"semestres\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:4 [\n        \"id\" => 1\n        \"nom\" => \"Semestre 1\"\n        \"niveau_id\" => 1\n        \"deleted_at\" => null\n      ]\n      #original: array:4 [\n        \"id\" => 1\n        \"nom\" => \"Semestre 1\"\n        \"niveau_id\" => 1\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: false\n      #hidden: []\n      #visible: []\n      #fillable: array:2 [\n        0 => \"nom\"\n        1 => \"niveau_id\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"current_niveau\" => App\\Models\\Niveau {#1858\n      #connection: \"mysql\"\n      #table: \"niveaux\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:4 [\n        \"id\" => 1\n        \"nom\" => \"1ère année\"\n        \"sigle\" => \"L1\"\n        \"deleted_at\" => null\n      ]\n      #original: array:4 [\n        \"id\" => 1\n        \"nom\" => \"1ère année\"\n        \"sigle\" => \"L1\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"current_annee\" => App\\Models\\AnneeUniversitaire {#1867\n      #connection: \"mysql\"\n      #table: \"annee_universitaires\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:3 [\n        \"id\" => 4\n        \"nom\" => \"2022/2023\"\n        \"deleted_at\" => null\n      ]\n      #original: array:3 [\n        \"id\" => 4\n        \"nom\" => \"2022/2023\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: false\n      #hidden: []\n      #visible: []\n      #fillable: array:1 [\n        0 => \"nom\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"current_parcour\" => App\\Models\\Parcour {#1876\n      #connection: \"mysql\"\n      #table: \"parcours\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 5\n        \"sigle\" => \"TD\"\n        \"nom\" => \"Transit et Douane\"\n        \"mention_id\" => 2\n        \"created_at\" => null\n        \"updated_at\" => null\n        \"deleted_at\" => null\n      ]\n      #original: array:7 [\n        \"id\" => 5\n        \"sigle\" => \"TD\"\n        \"nom\" => \"Transit et Douane\"\n        \"mention_id\" => 2\n        \"created_at\" => null\n        \"updated_at\" => null\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:3 [\n        0 => \"nom\"\n        1 => \"sigle\"\n        2 => \"mention_id\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"selectedSemestreId\" => \"\"\n    \"viewMode\" => \"table\"\n    \"searchQuery\" => \"\"\n    \"sortBy\" => \"created_at_desc\"\n    \"selectedNoteType\" => 1\n    \"autoSave\" => true\n    \"saveStatus\" => []\n    \"notesStatistics\" => array:5 [\n      \"average\" => 11.609444444444\n      \"best\" => 17.22\n      \"worst\" => 0.0\n      \"count\" => 18\n      \"success_rate\" => 77.777777777778\n    ]\n    \"validationErrors\" => []\n    \"isValidating\" => false\n    \"page\" => \"70\"\n    \"paginators\" => array:1 [\n      \"page\" => \"70\"\n    ]\n  ]\n  \"name\" => \"etudiant\"\n  \"view\" => \"livewire.deraq.etudiant.notes\"\n  \"component\" => \"App\\Http\\Livewire\\Etudiant\"\n  \"id\" => \"hoJJz2TNBUmyOStN1MHq\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/gestions/etudiants?page=70\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1751977751\n]"}, "request": {"path_info": "/livewire/message/etudiant", "status_code": "<pre class=sf-dump id=sf-dump-820247100 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-820247100\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1312654582 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1312654582\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-537484156 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">hoJJz2TNBUmyOStN1MHq</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">etudiant</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"18 characters\">gestions/etudiants</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">8c36319d</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:54</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>showCreateModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showEditModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showDeleteModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showRestoreModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>viewSupprimesMode</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>selectedEtudiantId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>selectedUserId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>etuName</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>newUser</span>\" => []\n      \"<span class=sf-dump-key>editUser</span>\" => []\n      \"<span class=sf-dump-key>editParcours</span>\" => []\n      \"<span class=sf-dump-key>query</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filtreParcours</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filtreNiveau</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filtreAnnee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>perPage</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>isLoading</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>sortField</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>sortDirection</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n      \"<span class=sf-dump-key>showFilters</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>compactView</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>bulkAction</span>\" => \"\"\n      \"<span class=sf-dump-key>totalEtudiants</span>\" => <span class=sf-dump-num>720</span>\n      \"<span class=sf-dump-key>totalSupprim&#233;s</span>\" => <span class=sf-dump-num>102</span>\n      \"<span class=sf-dump-key>selectedItems</span>\" => []\n      \"<span class=sf-dump-key>showNotes</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>selectedStudentName</span>\" => \"<span class=sf-dump-str title=\"16 characters\">BEHAVANA Gauffit</span>\"\n      \"<span class=sf-dump-key>noteUserId</span>\" => <span class=sf-dump-num>109</span>\n      \"<span class=sf-dump-key>noteParcourId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>noteMatiereId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>noteTypeId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>noteValeur</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>noteObservation</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>noteActionMode</span>\" => \"<span class=sf-dump-str title=\"4 characters\">list</span>\"\n      \"<span class=sf-dump-key>selectedNoteId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>notes</span>\" => []\n      \"<span class=sf-dump-key>matieres</span>\" => []\n      \"<span class=sf-dump-key>noteTypes</span>\" => []\n      \"<span class=sf-dump-key>current_semestre</span>\" => []\n      \"<span class=sf-dump-key>current_niveau</span>\" => []\n      \"<span class=sf-dump-key>current_annee</span>\" => []\n      \"<span class=sf-dump-key>current_parcour</span>\" => []\n      \"<span class=sf-dump-key>selectedSemestreId</span>\" => \"\"\n      \"<span class=sf-dump-key>viewMode</span>\" => \"<span class=sf-dump-str title=\"5 characters\">table</span>\"\n      \"<span class=sf-dump-key>searchQuery</span>\" => \"\"\n      \"<span class=sf-dump-key>sortBy</span>\" => \"<span class=sf-dump-str title=\"15 characters\">created_at_desc</span>\"\n      \"<span class=sf-dump-key>selectedNoteType</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>autoSave</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>saveStatus</span>\" => []\n      \"<span class=sf-dump-key>notesStatistics</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>average</span>\" => <span class=sf-dump-num>11.609444444444</span>\n        \"<span class=sf-dump-key>best</span>\" => <span class=sf-dump-num>17.22</span>\n        \"<span class=sf-dump-key>worst</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>count</span>\" => <span class=sf-dump-num>18</span>\n        \"<span class=sf-dump-key>success_rate</span>\" => <span class=sf-dump-num>77.777777777778</span>\n      </samp>]\n      \"<span class=sf-dump-key>validationErrors</span>\" => []\n      \"<span class=sf-dump-key>isValidating</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">70</span>\"\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">70</span>\"\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>modelCollections</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>notes</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Note</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-note>array:18</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10139</span>\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-num>9979</span>\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-num>9859</span>\n            <span class=sf-dump-index>3</span> => <span class=sf-dump-num>9819</span>\n            <span class=sf-dump-index>4</span> => <span class=sf-dump-num>9699</span>\n            <span class=sf-dump-index>5</span> => <span class=sf-dump-num>8480</span>\n            <span class=sf-dump-index>6</span> => <span class=sf-dump-num>8415</span>\n            <span class=sf-dump-index>7</span> => <span class=sf-dump-num>8302</span>\n            <span class=sf-dump-index>8</span> => <span class=sf-dump-num>4461</span>\n            <span class=sf-dump-index>9</span> => <span class=sf-dump-num>4180</span>\n            <span class=sf-dump-index>10</span> => <span class=sf-dump-num>2677</span>\n            <span class=sf-dump-index>11</span> => <span class=sf-dump-num>1625</span>\n            <span class=sf-dump-index>12</span> => <span class=sf-dump-num>1511</span>\n            <span class=sf-dump-index>13</span> => <span class=sf-dump-num>1397</span>\n            <span class=sf-dump-index>14</span> => <span class=sf-dump-num>1295</span>\n            <span class=sf-dump-index>15</span> => <span class=sf-dump-num>1163</span>\n            <span class=sf-dump-index>16</span> => <span class=sf-dump-num>1106</span>\n            <span class=sf-dump-index>17</span> => <span class=sf-dump-num>885</span>\n          </samp>]\n          \"<span class=sf-dump-key>relations</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">matiere</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">typeNote</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>noteTypes</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"19 characters\">App\\Models\\TypeNote</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>1</span>\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-num>2</span>\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-num>3</span>\n          </samp>]\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>models</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>current_semestre</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"19 characters\">App\\Models\\Semestre</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>current_niveau</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Niveau</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>current_annee</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"29 characters\">App\\Models\\AnneeUniversitaire</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>current_parcour</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Parcour</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">c2fa5a46cddc4eca945ff406ad12719a56a3581575b9e55fb5651fc8a7d30f9c</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">snob</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">$set</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">selectedNoteType</span>\"\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-num>1</span>\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-537484156\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-824803922 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2421</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/gestions/etudiants?page=70</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjJjSDU2T1VNTWNBaVl6TVV0ZGRnaUE9PSIsInZhbHVlIjoiNk5rQUsyK1VnV2Q4S0NEbk03b1BNMkJ3OWxicDBicFpCNE45cGg4WGw1SVV3WUVRMlVpSlZZRVZnM0xLeWhIUTVLOEhhcDB3UGFQMmlXNWViSS8wcGtoT2lyWFExaHZQZ3JWS2UwNTVWUTI2MVlCTmdsMitWNEtXdDBxQjdOdG8iLCJtYWMiOiI4N2IyMzJhMTRlYmIyOGJmN2MzYTcxNjhlMzVkYTlkZWFmMTE1NzFkYzUzZTdhMWIzMjY4N2Y3YWRmZGZiYTQ5IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IjRYeWU4enZmM2d6RDNYZ0JWakpUbFE9PSIsInZhbHVlIjoibG9OOGgrTkpMUFRXY3BNWnlOekJWWVBYVWpKQzYxS2NxZHJPdWlHR2FJWnFnMmZIcUJpN28vZ28za0NUZHRTdEFDNlVmQm9CYnJIQU9iWkxzM0pjWEp0ekVSZDg3QUVsaXc4Sks2OWUycXJNSjgrdXpCQ2ZqN1pZd012emlYU0YiLCJtYWMiOiI0YmYzNDY3Y2ZmY2IzOTgzNjRlMjU1ZjIzMjYxNTdlODk3ZTQ0ZmRlMmJiMzJlOTY1NmY0MTNmNTI0ZjIwYWRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-824803922\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-554991567 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52360</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/etudiant</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/etudiant</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"36 characters\">/index.php/livewire/message/etudiant</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2421</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2421</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/gestions/etudiants?page=70</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjJjSDU2T1VNTWNBaVl6TVV0ZGRnaUE9PSIsInZhbHVlIjoiNk5rQUsyK1VnV2Q4S0NEbk03b1BNMkJ3OWxicDBicFpCNE45cGg4WGw1SVV3WUVRMlVpSlZZRVZnM0xLeWhIUTVLOEhhcDB3UGFQMmlXNWViSS8wcGtoT2lyWFExaHZQZ3JWS2UwNTVWUTI2MVlCTmdsMitWNEtXdDBxQjdOdG8iLCJtYWMiOiI4N2IyMzJhMTRlYmIyOGJmN2MzYTcxNjhlMzVkYTlkZWFmMTE1NzFkYzUzZTdhMWIzMjY4N2Y3YWRmZGZiYTQ5IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IjRYeWU4enZmM2d6RDNYZ0JWakpUbFE9PSIsInZhbHVlIjoibG9OOGgrTkpMUFRXY3BNWnlOekJWWVBYVWpKQzYxS2NxZHJPdWlHR2FJWnFnMmZIcUJpN28vZ28za0NUZHRTdEFDNlVmQm9CYnJIQU9iWkxzM0pjWEp0ekVSZDg3QUVsaXc4Sks2OWUycXJNSjgrdXpCQ2ZqN1pZd012emlYU0YiLCJtYWMiOiI0YmYzNDY3Y2ZmY2IzOTgzNjRlMjU1ZjIzMjYxNTdlODk3ZTQ0ZmRlMmJiMzJlOTY1NmY0MTNmNTI0ZjIwYWRhIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751978872.0236</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751978872</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-554991567\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-269490983 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IC6ylzyOLsmp8eqEPKRtYbNYwxNcQ3yBFxnhfM9e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-269490983\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2011629989 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 08 Jul 2025 12:47:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImFuVjJvYnJxMzZna1d1SVJGSzBmR2c9PSIsInZhbHVlIjoicGZqYU92aU1veUsvRlFDWEcrdWhlS1JSY1dpekZQQnZHU1RQNDBsbDZHVm5Qc2RmVzdjV1l1YUdKOG82c1ZiNmordlJFS1F0RittQ2FEU0s4Wjd5QnpRUHJZQjFvRTRyelpEazhIUHN5eld6OW94MGg4TktPOEd4MHpiUitZZDUiLCJtYWMiOiI1OTU5ODIyZDUwN2UxYTc5ZGY1NTcxYjM5ZjZkZTYxNDQ1ODQxN2FhNzU5OGE4NjlmMDc5YjhmNjVlYzg1NDhmIiwidGFnIjoiIn0%3D; expires=Tue, 08 Jul 2025 14:47:54 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6ImhxMG55YzdiT0xJOEREa0RSS3BaL3c9PSIsInZhbHVlIjoidDZtdXBTMGphUkxXVGpHbTY0UlZIdzBoMC9tRFpwdTJGZ0xtZzcvTlBsaFpQQmoyN0ZHNmJhYVlGZng4V0VwTmlrSGY5MFp1c3NhVE9pVU9kbU82NytEcWZlRUUrSlFFbnFpYys0dTdQMjNnclNyRCsyOUdKSU42aHIyL2xlWUMiLCJtYWMiOiJkM2YzZjQzMWU0NmZkMGJhNDJmZTBhMzZkMjNjZGU5ZGZkZDc3NDlhZDgxNTg0ZTNlZTU1Y2I5MjE3MmNmY2U5IiwidGFnIjoiIn0%3D; expires=Tue, 08 Jul 2025 14:47:54 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImFuVjJvYnJxMzZna1d1SVJGSzBmR2c9PSIsInZhbHVlIjoicGZqYU92aU1veUsvRlFDWEcrdWhlS1JSY1dpekZQQnZHU1RQNDBsbDZHVm5Qc2RmVzdjV1l1YUdKOG82c1ZiNmordlJFS1F0RittQ2FEU0s4Wjd5QnpRUHJZQjFvRTRyelpEazhIUHN5eld6OW94MGg4TktPOEd4MHpiUitZZDUiLCJtYWMiOiI1OTU5ODIyZDUwN2UxYTc5ZGY1NTcxYjM5ZjZkZTYxNDQ1ODQxN2FhNzU5OGE4NjlmMDc5YjhmNjVlYzg1NDhmIiwidGFnIjoiIn0%3D; expires=Tue, 08-Jul-2025 14:47:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6ImhxMG55YzdiT0xJOEREa0RSS3BaL3c9PSIsInZhbHVlIjoidDZtdXBTMGphUkxXVGpHbTY0UlZIdzBoMC9tRFpwdTJGZ0xtZzcvTlBsaFpQQmoyN0ZHNmJhYVlGZng4V0VwTmlrSGY5MFp1c3NhVE9pVU9kbU82NytEcWZlRUUrSlFFbnFpYys0dTdQMjNnclNyRCsyOUdKSU42aHIyL2xlWUMiLCJtYWMiOiJkM2YzZjQzMWU0NmZkMGJhNDJmZTBhMzZkMjNjZGU5ZGZkZDc3NDlhZDgxNTg0ZTNlZTU1Y2I5MjE3MmNmY2U5IiwidGFnIjoiIn0%3D; expires=Tue, 08-Jul-2025 14:47:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2011629989\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1880627787 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/gestions/etudiants?page=70</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1751977751</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1880627787\", {\"maxDepth\":0})</script>\n"}}