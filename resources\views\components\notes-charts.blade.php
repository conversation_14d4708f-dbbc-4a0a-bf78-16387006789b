<!-- Composant de graphiques pour les notes -->
<div class="notes-charts-container">
    <!-- Graphique principal -->
    <div class="row g-4 mb-4">
        <div class="col-lg-8">
            <div class="note-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">
                            <i class="fa fa-chart-line me-2 text-primary"></i>
                            Évolution des notes
                        </h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary active" onclick="showChart('line')">
                                <i class="fa fa-chart-line"></i> Courbe
                            </button>
                            <button type="button" class="btn btn-outline-primary" onclick="showChart('bar')">
                                <i class="fa fa-chart-bar"></i> Barres
                            </button>
                            <button type="button" class="btn btn-outline-primary" onclick="showChart('radar')">
                                <i class="fa fa-chart-area"></i> Radar
                            </button>
                        </div>
                    </div>
                    <canvas id="notesEvolutionChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Répartition par matière -->
            <div class="note-card mb-3">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fa fa-pie-chart me-2 text-success"></i>
                        Répartition par matière
                    </h6>
                    <canvas id="matieresPieChart" width="200" height="200"></canvas>
                </div>
            </div>
            
            <!-- Répartition par type -->
            <div class="note-card">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fa fa-doughnut-chart me-2 text-info"></i>
                        Types d'évaluation
                    </h6>
                    <canvas id="typesDoughnutChart" width="200" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistiques détaillées -->
    <div class="row g-4 mb-4">
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stats-icon mb-3">
                    <i class="fa fa-chart-line fa-2x"></i>
                </div>
                <h3 class="stats-value" id="averageValue">0.00</h3>
                <p class="stats-label">Moyenne générale</p>
                <div class="stats-trend" id="averageTrend">
                    <i class="fa fa-arrow-up text-success"></i> +0.5
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stats-icon mb-3">
                    <i class="fa fa-trophy fa-2x"></i>
                </div>
                <h3 class="stats-value" id="bestValue">0.00</h3>
                <p class="stats-label">Meilleure note</p>
                <div class="stats-subject" id="bestSubject">-</div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stats-icon mb-3">
                    <i class="fa fa-target fa-2x"></i>
                </div>
                <h3 class="stats-value" id="successRate">0%</h3>
                <p class="stats-label">Taux de réussite</p>
                <div class="stats-detail" id="successDetail">0/0 notes ≥ 10</div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stats-icon mb-3">
                    <i class="fa fa-clipboard-list fa-2x"></i>
                </div>
                <h3 class="stats-value" id="totalNotes">0</h3>
                <p class="stats-label">Notes enregistrées</p>
                <div class="stats-detail" id="lastNoteDate">-</div>
            </div>
        </div>
    </div>
    
    <!-- Analyse par matière -->
    <div class="note-card">
        <div class="card-body">
            <h5 class="card-title mb-4">
                <i class="fa fa-book me-2 text-primary"></i>
                Analyse par matière
            </h5>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Matière</th>
                            <th class="text-center">Nombre de notes</th>
                            <th class="text-center">Moyenne</th>
                            <th class="text-center">Meilleure note</th>
                            <th class="text-center">Progression</th>
                        </tr>
                    </thead>
                    <tbody id="matiereAnalysisTable">
                        <!-- Contenu généré dynamiquement -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<style>
.notes-charts-container {
    padding: 1rem 0;
}

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 1rem;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: shimmer 3s ease-in-out infinite;
}

.stats-icon {
    opacity: 0.8;
}

.stats-value {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

.stats-label {
    margin: 0;
    opacity: 0.9;
    font-size: 0.9rem;
}

.stats-trend {
    font-size: 0.8rem;
    margin-top: 0.5rem;
}

.stats-subject, .stats-detail {
    font-size: 0.8rem;
    opacity: 0.8;
    margin-top: 0.5rem;
}

@keyframes shimmer {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(180deg); }
}

/* Responsive pour les graphiques */
@media (max-width: 768px) {
    .notes-charts-container {
        padding: 0.5rem 0;
    }
    
    .stats-card {
        margin-bottom: 1rem;
        padding: 1rem;
    }
    
    .stats-value {
        font-size: 1.5rem;
    }
    
    canvas {
        max-height: 250px;
    }
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let notesEvolutionChart, matieresPieChart, typesDoughnutChart;

// Initialisation des graphiques
function initializeCharts(notesData) {
    initEvolutionChart(notesData);
    initMatieresChart(notesData);
    initTypesChart(notesData);
    updateStatistics(notesData);
    updateMatiereAnalysis(notesData);
}

// Graphique d'évolution des notes
function initEvolutionChart(notesData) {
    const ctx = document.getElementById('notesEvolutionChart').getContext('2d');
    
    // Préparer les données
    const sortedNotes = notesData.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
    const labels = sortedNotes.map(note => new Date(note.created_at).toLocaleDateString('fr-FR'));
    const values = sortedNotes.map(note => parseFloat(note.valeur));
    
    notesEvolutionChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Notes',
                data: values,
                borderColor: 'rgb(102, 126, 234)',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: 'rgb(102, 126, 234)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 20,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
}

// Graphique par matières
function initMatieresChart(notesData) {
    const ctx = document.getElementById('matieresPieChart').getContext('2d');
    
    // Compter les notes par matière
    const matiereCount = {};
    notesData.forEach(note => {
        const matiere = note.matiere?.nom || 'Non défini';
        matiereCount[matiere] = (matiereCount[matiere] || 0) + 1;
    });
    
    const colors = [
        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
        '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
    ];
    
    matieresPieChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: Object.keys(matiereCount),
            datasets: [{
                data: Object.values(matiereCount),
                backgroundColor: colors.slice(0, Object.keys(matiereCount).length),
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });
}

// Graphique par types
function initTypesChart(notesData) {
    const ctx = document.getElementById('typesDoughnutChart').getContext('2d');
    
    // Compter les notes par type
    const typeCount = {};
    notesData.forEach(note => {
        const type = note.type_note?.nom || 'Non défini';
        typeCount[type] = (typeCount[type] || 0) + 1;
    });
    
    const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe'];
    
    typesDoughnutChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: Object.keys(typeCount),
            datasets: [{
                data: Object.values(typeCount),
                backgroundColor: colors.slice(0, Object.keys(typeCount).length),
                borderWidth: 3,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '60%',
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 15,
                        usePointStyle: true
                    }
                }
            }
        }
    });
}

// Mise à jour des statistiques
function updateStatistics(notesData) {
    if (notesData.length === 0) return;
    
    const values = notesData.map(note => parseFloat(note.valeur));
    const average = values.reduce((a, b) => a + b, 0) / values.length;
    const best = Math.max(...values);
    const successCount = values.filter(v => v >= 10).length;
    const successRate = (successCount / values.length) * 100;
    
    document.getElementById('averageValue').textContent = average.toFixed(2);
    document.getElementById('bestValue').textContent = best.toFixed(2);
    document.getElementById('successRate').textContent = Math.round(successRate) + '%';
    document.getElementById('totalNotes').textContent = notesData.length;
    
    // Détails supplémentaires
    document.getElementById('successDetail').textContent = `${successCount}/${notesData.length} notes ≥ 10`;
    
    if (notesData.length > 0) {
        const lastNote = notesData.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))[0];
        document.getElementById('lastNoteDate').textContent = new Date(lastNote.created_at).toLocaleDateString('fr-FR');
        
        // Meilleure matière
        const bestNote = notesData.find(note => parseFloat(note.valeur) === best);
        document.getElementById('bestSubject').textContent = bestNote?.matiere?.nom || '-';
    }
}

// Analyse par matière
function updateMatiereAnalysis(notesData) {
    const matiereStats = {};
    
    notesData.forEach(note => {
        const matiere = note.matiere?.nom || 'Non défini';
        if (!matiereStats[matiere]) {
            matiereStats[matiere] = {
                notes: [],
                count: 0
            };
        }
        matiereStats[matiere].notes.push(parseFloat(note.valeur));
        matiereStats[matiere].count++;
    });
    
    const tableBody = document.getElementById('matiereAnalysisTable');
    tableBody.innerHTML = '';
    
    Object.entries(matiereStats).forEach(([matiere, stats]) => {
        const average = stats.notes.reduce((a, b) => a + b, 0) / stats.notes.length;
        const best = Math.max(...stats.notes);
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><strong>${matiere}</strong></td>
            <td class="text-center">${stats.count}</td>
            <td class="text-center">
                <span class="badge ${average >= 10 ? 'bg-success' : 'bg-danger'}">
                    ${average.toFixed(2)}
                </span>
            </td>
            <td class="text-center">
                <span class="badge bg-primary">${best.toFixed(2)}</span>
            </td>
            <td class="text-center">
                <i class="fa fa-arrow-up text-success"></i>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

// Changer le type de graphique
function showChart(type) {
    // Mettre à jour les boutons actifs
    document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
    event.target.closest('.btn').classList.add('active');
    
    // Changer le type de graphique
    if (notesEvolutionChart) {
        notesEvolutionChart.config.type = type;
        notesEvolutionChart.update();
    }
}

// Fonction publique pour mettre à jour les graphiques
window.updateNotesCharts = function(notesData) {
    initializeCharts(notesData);
};
</script>
