<!-- Modal de saisie de note modernisé -->
<div class="modal fade" id="noteFormModal" tabindex="-1" aria-labelledby="noteFormModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="noteFormModalLabel">
                    <i class="fa fa-plus-circle me-2"></i>
                    <span id="modalTitle">Ajouter une note</span>
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="noteForm" wire:submit.prevent="saveNote">
                    <div class="row g-3">
                        <!-- Sélection du parcours -->
                        <div class="col-md-6">
                            <label class="form-label fw-semibold">
                                <i class="fa fa-graduation-cap me-1 text-primary"></i>
                                Parcours <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" wire:model="noteParcourId" wire:change="updateParcour($event.target.value)">
                                <option value="">-- Sélectionner un parcours --</option>
                                <?php $__currentLoopData = $parcours ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($parc->id); ?>"><?php echo e($parc->sigle); ?> - <?php echo e($parc->nom); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <div class="invalid-feedback" id="parcours-error"></div>
                        </div>

                        <!-- Sélection de la matière -->
                        <div class="col-md-6">
                            <label class="form-label fw-semibold">
                                <i class="fa fa-book me-1 text-primary"></i>
                                Matière <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" wire:model="noteMatiereId" <?php if(!$noteParcourId): ?> disabled <?php endif; ?>>
                                <?php if(!$noteParcourId): ?>
                                    <option value="">-- Choisissez d'abord un parcours --</option>
                                <?php else: ?>
                                    <option value="">-- Sélectionner une matière --</option>
                                    <?php $__currentLoopData = $matieres ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($mat->id); ?>"><?php echo e($mat->nom); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </select>
                            <div class="invalid-feedback" id="matiere-error"></div>
                        </div>

                        <!-- Type de note -->
                        <div class="col-md-6">
                            <label class="form-label fw-semibold">
                                <i class="fa fa-tag me-1 text-primary"></i>
                                Type de note <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" wire:model="noteTypeId">
                                <option value="">-- Sélectionner un type --</option>
                                <?php $__currentLoopData = $noteTypes ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($type->id); ?>"><?php echo e($type->nom); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <div class="invalid-feedback" id="type-error"></div>
                        </div>

                        <!-- Note sur 20 -->
                        <div class="col-md-6">
                            <label class="form-label fw-semibold">
                                <i class="fa fa-star me-1 text-primary"></i>
                                Note sur 20 <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <input type="number" class="form-control" wire:model.lazy="noteValeur" 
                                       min="0" max="20" step="0.01" placeholder="0.00">
                                <span class="input-group-text">/20</span>
                            </div>
                            <div class="invalid-feedback" id="valeur-error"></div>
                            
                            <!-- Indicateur visuel de la note -->
                            <div class="mt-2" id="noteIndicator" style="display: none;">
                                <div class="d-flex align-items-center">
                                    <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <small class="text-muted" id="noteLabel"></small>
                                </div>
                            </div>
                        </div>

                        <!-- Observation -->
                        <div class="col-12">
                            <label class="form-label fw-semibold">
                                <i class="fa fa-comment me-1 text-primary"></i>
                                Observation
                            </label>
                            <textarea class="form-control" wire:model.lazy="noteObservation" 
                                      rows="3" placeholder="Commentaire ou observation sur cette note..."></textarea>
                            <div class="form-text">
                                <i class="fa fa-info-circle me-1"></i>
                                Ajoutez des détails sur cette évaluation (optionnel)
                            </div>
                        </div>

                        <!-- Aperçu de la note -->
                        <div class="col-12" id="notePreview" style="display: none;">
                            <div class="alert alert-info">
                                <h6 class="alert-heading">
                                    <i class="fa fa-eye me-1"></i>Aperçu de la note
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Matière:</strong> <span id="previewMatiere">-</span><br>
                                        <strong>Type:</strong> <span id="previewType">-</span>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Note:</strong> <span id="previewNote" class="fw-bold">-</span>/20<br>
                                        <strong>Appréciation:</strong> <span id="previewAppreciation">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <div class="d-flex justify-content-between w-100">
                    <div>
                        <!-- Statut de sauvegarde -->
                        <span id="saveStatus" class="text-muted" style="display: none;">
                            <i class="fa fa-spinner fa-spin me-1"></i>Sauvegarde en cours...
                        </span>
                    </div>
                    <div>
                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                            <i class="fa fa-times me-1"></i>Annuler
                        </button>
                        <button type="submit" class="btn btn-primary" form="noteForm" id="saveBtn">
                            <i class="fa fa-save me-1"></i>
                            <span id="saveBtnText">Enregistrer</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.modal-content {
    border-radius: 1rem;
    border: none;
    box-shadow: 0 20px 60px rgba(0,0,0,0.2);
}

.modal-header {
    border-radius: 1rem 1rem 0 0;
    border-bottom: none;
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 1rem 1rem;
    padding: 1.5rem;
}

.form-label {
    margin-bottom: 0.5rem;
    color: #495057;
}

.form-select, .form-control {
    border-radius: 0.5rem;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-select:focus, .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.input-group-text {
    border-radius: 0 0.5rem 0.5rem 0;
    border: 2px solid #e9ecef;
    border-left: none;
    background: #f8f9fa;
    font-weight: 600;
}

.progress {
    border-radius: 10px;
    background: #e9ecef;
}

.progress-bar {
    border-radius: 10px;
    transition: all 0.3s ease;
}

.alert {
    border-radius: 0.75rem;
    border: none;
}

.btn {
    border-radius: 0.5rem;
    padding: 0.5rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* Animations */
.modal.fade .modal-dialog {
    transform: scale(0.8) translateY(-50px);
    transition: all 0.3s ease;
}

.modal.show .modal-dialog {
    transform: scale(1) translateY(0);
}

/* Responsive */
@media (max-width: 768px) {
    .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
    }
    
    .modal-body, .modal-footer {
        padding: 1rem;
    }
}
</style>

<script>
// Gestion de l'aperçu en temps réel
document.addEventListener('DOMContentLoaded', function() {
    const noteInput = document.querySelector('input[wire\\:model\\.lazy="noteValeur"]');
    const matiereSelect = document.querySelector('select[wire\\:model="noteMatiereId"]');
    const typeSelect = document.querySelector('select[wire\\:model="noteTypeId"]');
    
    function updateNoteIndicator(value) {
        const indicator = document.getElementById('noteIndicator');
        const progressBar = indicator.querySelector('.progress-bar');
        const label = document.getElementById('noteLabel');
        
        if (value && value >= 0 && value <= 20) {
            indicator.style.display = 'block';
            const percentage = (value / 20) * 100;
            progressBar.style.width = percentage + '%';
            
            // Couleurs et labels selon la note
            if (value >= 16) {
                progressBar.className = 'progress-bar bg-success';
                label.textContent = 'Excellent';
            } else if (value >= 14) {
                progressBar.className = 'progress-bar bg-info';
                label.textContent = 'Bien';
            } else if (value >= 10) {
                progressBar.className = 'progress-bar bg-warning';
                label.textContent = 'Passable';
            } else {
                progressBar.className = 'progress-bar bg-danger';
                label.textContent = 'Insuffisant';
            }
        } else {
            indicator.style.display = 'none';
        }
    }
    
    function updatePreview() {
        const preview = document.getElementById('notePreview');
        const matiere = matiereSelect?.selectedOptions[0]?.text || '-';
        const type = typeSelect?.selectedOptions[0]?.text || '-';
        const note = noteInput?.value || '-';
        
        if (matiere !== '-' && type !== '-' && note !== '-') {
            document.getElementById('previewMatiere').textContent = matiere;
            document.getElementById('previewType').textContent = type;
            document.getElementById('previewNote').textContent = note;
            
            // Appréciation
            const appreciation = note >= 16 ? 'Excellent' : 
                               note >= 14 ? 'Bien' : 
                               note >= 10 ? 'Passable' : 'Insuffisant';
            document.getElementById('previewAppreciation').textContent = appreciation;
            
            preview.style.display = 'block';
        } else {
            preview.style.display = 'none';
        }
    }
    
    // Écouteurs d'événements
    if (noteInput) {
        noteInput.addEventListener('input', function() {
            updateNoteIndicator(this.value);
            updatePreview();
        });
    }
    
    if (matiereSelect) {
        matiereSelect.addEventListener('change', updatePreview);
    }
    
    if (typeSelect) {
        typeSelect.addEventListener('change', updatePreview);
    }
});

// Raccourcis clavier
document.addEventListener('keydown', function(e) {
    if (document.getElementById('noteFormModal').classList.contains('show')) {
        // Ctrl+S pour sauvegarder
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            document.getElementById('saveBtn').click();
        }
        
        // Escape pour fermer
        if (e.key === 'Escape') {
            const modal = bootstrap.Modal.getInstance(document.getElementById('noteFormModal'));
            modal.hide();
        }
    }
});
</script>
<?php /**PATH C:\xampp\htdocs\ImsaaProject\resources\views/components/note-form-modal.blade.php ENDPATH**/ ?>