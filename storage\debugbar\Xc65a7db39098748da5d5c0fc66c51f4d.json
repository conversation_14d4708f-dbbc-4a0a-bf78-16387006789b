{"__meta": {"id": "Xc65a7db39098748da5d5c0fc66c51f4d", "datetime": "2025-07-08 15:29:52", "utime": 1751977792.546295, "method": "POST", "uri": "/livewire/message/etudiant", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751977790.763422, "end": 1751977792.546358, "duration": 1.7829360961914062, "duration_str": "1.78s", "measures": [{"label": "Booting", "start": 1751977790.763422, "relative_start": 0, "end": 1751977791.368192, "relative_end": 1751977791.368192, "duration": 0.6047699451446533, "duration_str": "605ms", "params": [], "collector": null}, {"label": "Application", "start": 1751977791.369379, "relative_start": 0.60595703125, "end": 1751977792.546366, "relative_end": 7.867813110351562e-06, "duration": 1.1769869327545166, "duration_str": "1.18s", "params": [], "collector": null}]}, "memory": {"peak_usage": 26730344, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "livewire.deraq.etudiant.index (\\resources\\views\\livewire\\deraq\\etudiant\\index.blade.php)", "param_count": 52, "params": ["etudiants", "parcours", "annees", "niveaux", "livewireLayout", "errors", "_instance", "showCreateModal", "showEditModal", "showDeleteModal", "showRestoreModal", "viewSupprimesMode", "selectedEtudiantId", "selectedUserId", "etuName", "newUser", "editUser", "editParcours", "query", "filtreParcours", "filtre<PERSON><PERSON>au", "filtreAnnee", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "bulkAction", "totalEtudiants", "totalSupprimés", "selectedItems", "showNotes", "selectedStudentName", "noteUserId", "noteParcourId", "noteMatiereId", "noteTypeId", "noteValeur", "noteObservation", "noteActionMode", "selectedNoteId", "notes", "matieres", "noteTypes", "current_semestre", "current_niveau", "current_annee", "current_parcour", "selectedSemestreId", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/etudiant/index.blade.php&line=0"}, {"name": "livewire::bootstrap (\\vendor\\livewire\\livewire\\src\\views\\pagination\\bootstrap.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\views\\pagination/bootstrap.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.053, "accumulated_duration_str": "53ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.01731, "duration_str": "17.31ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 32.66}, {"sql": "select count(*) as aggregate from `inscription_students` where exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `inscription_students`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00933, "duration_str": "9.33ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 32.66, "width_percent": 17.604}, {"sql": "select `id`, `user_id`, `parcour_id`, `niveau_id`, `annee_universitaire_id`, `created_at`, `deleted_at` from `inscription_students` where exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `inscription_students`.`deleted_at` is null order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.006809999999999999, "duration_str": "6.81ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 50.264, "width_percent": 12.849}, {"sql": "select `id`, `nom`, `prenom`, `telephone1`, `sexe`, `photo` from `users` where `users`.`id` in (493, 494, 495, 496, 497, 498, 499, 503, 504, 505) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0012900000000000001, "duration_str": "1.29ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 63.113, "width_percent": 2.434}, {"sql": "select `id`, `sigle`, `nom` from `parcours` where `parcours`.`id` in (1, 4, 7, 8, 22, 24) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.01277, "duration_str": "12.77ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 65.547, "width_percent": 24.094}, {"sql": "select `id`, `nom` from `niveaux` where `niveaux`.`id` in (1, 2, 4) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00436, "duration_str": "4.36ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 89.642, "width_percent": 8.226}, {"sql": "select `id`, `nom` from `annee_universitaires` where `annee_universitaires`.`id` in (6) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00113, "duration_str": "1.13ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 97.868, "width_percent": 2.132}]}, "models": {"data": {"App\\Models\\AnneeUniversitaire": 1, "App\\Models\\Niveau": 3, "App\\Models\\Parcour": 6, "App\\Models\\InscriptionStudent": 10, "App\\Models\\User": 11}, "count": 31}, "livewire": {"data": {"etudiant #BNqp46jYhUgGtP22GbVn": "array:5 [\n  \"data\" => array:45 [\n    \"showCreateModal\" => false\n    \"showEditModal\" => false\n    \"showDeleteModal\" => false\n    \"showRestoreModal\" => false\n    \"viewSupprimesMode\" => false\n    \"selectedEtudiantId\" => null\n    \"selectedUserId\" => null\n    \"etuName\" => null\n    \"newUser\" => []\n    \"editUser\" => []\n    \"editParcours\" => []\n    \"query\" => null\n    \"filtreParcours\" => null\n    \"filtreNiveau\" => null\n    \"filtreAnnee\" => null\n    \"perPage\" => 10\n    \"isLoading\" => false\n    \"sortField\" => \"created_at\"\n    \"sortDirection\" => \"desc\"\n    \"showFilters\" => true\n    \"compactView\" => true\n    \"bulkAction\" => \"\"\n    \"totalEtudiants\" => 720\n    \"totalSupprimés\" => 102\n    \"selectedItems\" => []\n    \"showNotes\" => false\n    \"selectedStudentName\" => \"\"\n    \"noteUserId\" => null\n    \"noteParcourId\" => null\n    \"noteMatiereId\" => null\n    \"noteTypeId\" => null\n    \"noteValeur\" => null\n    \"noteObservation\" => null\n    \"noteActionMode\" => \"list\"\n    \"selectedNoteId\" => null\n    \"notes\" => []\n    \"matieres\" => []\n    \"noteTypes\" => []\n    \"current_semestre\" => null\n    \"current_niveau\" => null\n    \"current_annee\" => null\n    \"current_parcour\" => null\n    \"selectedSemestreId\" => \"\"\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"etudiant\"\n  \"view\" => \"livewire.deraq.etudiant.index\"\n  \"component\" => \"App\\Http\\Livewire\\Etudiant\"\n  \"id\" => \"BNqp46jYhUgGtP22GbVn\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/gestions/etudiants\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1751977751\n]"}, "request": {"path_info": "/livewire/message/etudiant", "status_code": "<pre class=sf-dump id=sf-dump-1135565355 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1135565355\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1891457007 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1891457007\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2066652741 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">BNqp46jYhUgGtP22GbVn</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">etudiant</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"18 characters\">gestions/etudiants</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">7ff73252</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:45</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>showCreateModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showEditModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showDeleteModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showRestoreModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>viewSupprimesMode</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>selectedEtudiantId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>selectedUserId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>etuName</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>newUser</span>\" => []\n      \"<span class=sf-dump-key>editUser</span>\" => []\n      \"<span class=sf-dump-key>editParcours</span>\" => []\n      \"<span class=sf-dump-key>query</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filtreParcours</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filtreNiveau</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filtreAnnee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>perPage</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>isLoading</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>sortField</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>sortDirection</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n      \"<span class=sf-dump-key>showFilters</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>compactView</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>bulkAction</span>\" => \"\"\n      \"<span class=sf-dump-key>totalEtudiants</span>\" => <span class=sf-dump-num>720</span>\n      \"<span class=sf-dump-key>totalSupprim&#233;s</span>\" => <span class=sf-dump-num>102</span>\n      \"<span class=sf-dump-key>selectedItems</span>\" => []\n      \"<span class=sf-dump-key>showNotes</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>selectedStudentName</span>\" => \"\"\n      \"<span class=sf-dump-key>noteUserId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>noteParcourId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>noteMatiereId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>noteTypeId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>noteValeur</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>noteObservation</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>noteActionMode</span>\" => \"<span class=sf-dump-str title=\"4 characters\">list</span>\"\n      \"<span class=sf-dump-key>selectedNoteId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>notes</span>\" => []\n      \"<span class=sf-dump-key>matieres</span>\" => []\n      \"<span class=sf-dump-key>noteTypes</span>\" => []\n      \"<span class=sf-dump-key>current_semestre</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>current_niveau</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>current_annee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>current_parcour</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>selectedSemestreId</span>\" => \"\"\n      \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">a54d251466e02630980db0ebd2864a3c112b0ac02ecbf1593a5e37eea64cab63</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"5 characters\">s6hkk</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"17 characters\">toggleCompactView</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2066652741\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-205781229 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1288</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/gestions/etudiants</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IndieCtndDZ4VEZ1OUJaZjQ3VzlpZmc9PSIsInZhbHVlIjoiUjVvVVYwTHFrWUp6SVBIamhPWGdvZnhPQjdGbWNoZTBhVHVTb0ZJcm4wSUF0K3JyOXk5bUFsUHA1QnZVb3V2S2VxNDBteHdFOWR5akZ4aFowRHlJUEJlakJKOG0wK0tjMW0xaU1KdksrODdBV0V0eVkyajdrSm8vMnNzZERaMG0iLCJtYWMiOiIzMGI4NzU2ZTQ1NDg2YWQyNTJkNWJkZGQ3MmM1NmJiMzhjMjBlYjAzMzE4YWVjZGFkNzgxZTg5NjdjMTkxODg3IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IjAySUVuUkJ1NUQ1S1grd0drYjB6L2c9PSIsInZhbHVlIjoiQm55UVYzY01lVUZ1cDl4VGFHUnU5dHNUZnBGcnpueTdmN2FpK2xOY0pHMkNmdWhwVU1NRi9EYkl4REhxcVVSVTZTLzlJNmdlL1cwU1c2V2JiS0lXcTdiL283L0N0WEZWMzg4MDV1ZE5zVG11UzFka0ZPMXJuTkVaZUNrQjlDYTkiLCJtYWMiOiIyNGRlNmE5NmRmYmMzODQ1YTVjN2YxNDM3NjUxODZjMGIyMmJiODE2NDE5ZjFlNGFkMzkzNThmMWY5YmIzNDJkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-205781229\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-856110012 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">51578</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/etudiant</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/etudiant</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"36 characters\">/index.php/livewire/message/etudiant</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1288</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1288</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/gestions/etudiants</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IndieCtndDZ4VEZ1OUJaZjQ3VzlpZmc9PSIsInZhbHVlIjoiUjVvVVYwTHFrWUp6SVBIamhPWGdvZnhPQjdGbWNoZTBhVHVTb0ZJcm4wSUF0K3JyOXk5bUFsUHA1QnZVb3V2S2VxNDBteHdFOWR5akZ4aFowRHlJUEJlakJKOG0wK0tjMW0xaU1KdksrODdBV0V0eVkyajdrSm8vMnNzZERaMG0iLCJtYWMiOiIzMGI4NzU2ZTQ1NDg2YWQyNTJkNWJkZGQ3MmM1NmJiMzhjMjBlYjAzMzE4YWVjZGFkNzgxZTg5NjdjMTkxODg3IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IjAySUVuUkJ1NUQ1S1grd0drYjB6L2c9PSIsInZhbHVlIjoiQm55UVYzY01lVUZ1cDl4VGFHUnU5dHNUZnBGcnpueTdmN2FpK2xOY0pHMkNmdWhwVU1NRi9EYkl4REhxcVVSVTZTLzlJNmdlL1cwU1c2V2JiS0lXcTdiL283L0N0WEZWMzg4MDV1ZE5zVG11UzFka0ZPMXJuTkVaZUNrQjlDYTkiLCJtYWMiOiIyNGRlNmE5NmRmYmMzODQ1YTVjN2YxNDM3NjUxODZjMGIyMmJiODE2NDE5ZjFlNGFkMzkzNThmMWY5YmIzNDJkIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751977790.7634</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751977790</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-856110012\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-13041984 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IC6ylzyOLsmp8eqEPKRtYbNYwxNcQ3yBFxnhfM9e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-13041984\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 08 Jul 2025 12:29:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImFaZGIxb3VBV1gxUzBZQVcyYmxQclE9PSIsInZhbHVlIjoiMVUvY2RIUTBJbTVyU1o2OVUycWU2SENWQTk2cUhUYUczV296bmY4dDdlVENxajFJTFZUOTQzem83WTl3K0llTHI2RFpoWmFxMlJRd3p3MWVBNlhIc01VbnJUci9FTEpreWlnNUpZanY1UmNET0d4dUNRQ242cUpVT1FnSzFqNnIiLCJtYWMiOiJiN2U4ZjIzYTMyNTU3MzVmODQwOGU2MDQ1NDQzZGI0NTRlNDRmMTU0NmNhOWFmNzFmNjVjMWFlNWFiNjE4ZWVjIiwidGFnIjoiIn0%3D; expires=Tue, 08 Jul 2025 14:29:52 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6Iks4ZVBIUlgxMktOWDQxWUVacXlQL3c9PSIsInZhbHVlIjoiN1BzWGJCSWw4YzNuem9BNmtUb2NhS2RXNlp5NWFDeGRLNXNDLzdvTnBlZ0lDOVUvWUNWV1lKOExrUUZmU0ZGS1IyRFBBNU9aOExGdG1kY2dLNmR2cHZQaG9JSnVWdkNlaEhkWUhDeEZiRDVLakJwaHlWaWRlaENmVDQ2VENydGEiLCJtYWMiOiJlMjViNjA3NjdkZTcwZDU3Y2E3MzhkMjNlMGQyYmYyMTYzNWEwM2Y0YzgxOGRmNzJhYjgwMDgzNmYyNDliZWE4IiwidGFnIjoiIn0%3D; expires=Tue, 08 Jul 2025 14:29:52 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImFaZGIxb3VBV1gxUzBZQVcyYmxQclE9PSIsInZhbHVlIjoiMVUvY2RIUTBJbTVyU1o2OVUycWU2SENWQTk2cUhUYUczV296bmY4dDdlVENxajFJTFZUOTQzem83WTl3K0llTHI2RFpoWmFxMlJRd3p3MWVBNlhIc01VbnJUci9FTEpreWlnNUpZanY1UmNET0d4dUNRQ242cUpVT1FnSzFqNnIiLCJtYWMiOiJiN2U4ZjIzYTMyNTU3MzVmODQwOGU2MDQ1NDQzZGI0NTRlNDRmMTU0NmNhOWFmNzFmNjVjMWFlNWFiNjE4ZWVjIiwidGFnIjoiIn0%3D; expires=Tue, 08-Jul-2025 14:29:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6Iks4ZVBIUlgxMktOWDQxWUVacXlQL3c9PSIsInZhbHVlIjoiN1BzWGJCSWw4YzNuem9BNmtUb2NhS2RXNlp5NWFDeGRLNXNDLzdvTnBlZ0lDOVUvWUNWV1lKOExrUUZmU0ZGS1IyRFBBNU9aOExGdG1kY2dLNmR2cHZQaG9JSnVWdkNlaEhkWUhDeEZiRDVLakJwaHlWaWRlaENmVDQ2VENydGEiLCJtYWMiOiJlMjViNjA3NjdkZTcwZDU3Y2E3MzhkMjNlMGQyYmYyMTYzNWEwM2Y0YzgxOGRmNzJhYjgwMDgzNmYyNDliZWE4IiwidGFnIjoiIn0%3D; expires=Tue, 08-Jul-2025 14:29:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/gestions/etudiants</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1751977751</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}