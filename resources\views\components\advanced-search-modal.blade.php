<!-- Modal de recherche avancée -->
<div class="modal fade" id="advancedSearchModal" tabindex="-1" aria-labelledby="advancedSearchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="advancedSearchModalLabel">
                    <i class="fa fa-search me-2"></i>Recherche avancée
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="advancedSearchForm">
                    <div class="row g-3">
                        <!-- Informations personnelles -->
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fa fa-user me-1"></i>Informations personnelles
                            </h6>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label">Nom</label>
                            <input type="text" class="form-control" name="nom" placeholder="Nom de famille">
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label">Prénom</label>
                            <input type="text" class="form-control" name="prenom" placeholder="Prénom">
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label">Téléphone</label>
                            <input type="text" class="form-control" name="telephone" placeholder="Numéro de téléphone">
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label">Email</label>
                            <input type="email" class="form-control" name="email" placeholder="Adresse email">
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label">Sexe</label>
                            <select class="form-select" name="sexe">
                                <option value="">Tous</option>
                                <option value="M">Homme</option>
                                <option value="F">Femme</option>
                            </select>
                        </div>

                        <!-- Informations académiques -->
                        <div class="col-12 mt-4">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fa fa-graduation-cap me-1"></i>Informations académiques
                            </h6>
                        </div>
                        
                        <div class="col-md-4">
                            <label class="form-label">Parcours</label>
                            <select class="form-select" name="parcours" multiple>
                                @foreach($parcours ?? [] as $parcour)
                                    <option value="{{ $parcour->id }}">{{ $parcour->sigle }} - {{ $parcour->nom }}</option>
                                @endforeach
                            </select>
                            <small class="text-muted">Maintenez Ctrl pour sélectionner plusieurs</small>
                        </div>
                        
                        <div class="col-md-4">
                            <label class="form-label">Niveau</label>
                            <select class="form-select" name="niveau" multiple>
                                @foreach($niveaux ?? [] as $niveau)
                                    <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                @endforeach
                            </select>
                        </div>
                        
                        <div class="col-md-4">
                            <label class="form-label">Année universitaire</label>
                            <select class="form-select" name="annee" multiple>
                                @foreach($annees ?? [] as $annee)
                                    <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Dates -->
                        <div class="col-12 mt-4">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fa fa-calendar me-1"></i>Période d'inscription
                            </h6>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label">Date de début</label>
                            <input type="date" class="form-control" name="date_debut">
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label">Date de fin</label>
                            <input type="date" class="form-control" name="date_fin">
                        </div>

                        <!-- Options avancées -->
                        <div class="col-12 mt-4">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fa fa-cog me-1"></i>Options avancées
                            </h6>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label">Statut</label>
                            <select class="form-select" name="statut">
                                <option value="">Tous</option>
                                <option value="actif">Actifs</option>
                                <option value="supprime">Supprimés</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label">Parcours défini</label>
                            <select class="form-select" name="parcours_defini">
                                <option value="">Tous</option>
                                <option value="oui">Avec parcours</option>
                                <option value="non">Sans parcours</option>
                            </select>
                        </div>
                        
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="avec_notes" id="avecNotes">
                                <label class="form-check-label" for="avecNotes">
                                    Uniquement les étudiants avec des notes
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="avec_paiements" id="avecPaiements">
                                <label class="form-check-label" for="avecPaiements">
                                    Uniquement les étudiants avec des paiements
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" onclick="resetAdvancedSearch()">
                    <i class="fa fa-undo me-1"></i>Réinitialiser
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    Annuler
                </button>
                <button type="button" class="btn btn-primary" onclick="applyAdvancedSearch()">
                    <i class="fa fa-search me-1"></i>Rechercher
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function resetAdvancedSearch() {
    document.getElementById('advancedSearchForm').reset();
}

function applyAdvancedSearch() {
    const form = document.getElementById('advancedSearchForm');
    const formData = new FormData(form);
    const searchParams = {};
    
    // Collecter tous les paramètres de recherche
    for (let [key, value] of formData.entries()) {
        if (value.trim() !== '') {
            if (searchParams[key]) {
                // Si la clé existe déjà, créer un tableau
                if (Array.isArray(searchParams[key])) {
                    searchParams[key].push(value);
                } else {
                    searchParams[key] = [searchParams[key], value];
                }
            } else {
                searchParams[key] = value;
            }
        }
    }
    
    // Gérer les sélections multiples
    const multiSelects = form.querySelectorAll('select[multiple]');
    multiSelects.forEach(select => {
        const selectedValues = Array.from(select.selectedOptions).map(option => option.value);
        if (selectedValues.length > 0) {
            searchParams[select.name] = selectedValues;
        }
    });
    
    // Gérer les checkboxes
    const checkboxes = form.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        searchParams[checkbox.name] = checkbox.checked;
    });
    
    // Émettre l'événement vers Livewire
    window.dispatchEvent(new CustomEvent('advanced-search', {
        detail: searchParams
    }));
    
    // Fermer le modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('advancedSearchModal'));
    modal.hide();
    
    // Afficher un message de confirmation
    showToast('info', 'Recherche avancée appliquée avec ' + Object.keys(searchParams).length + ' critère(s)');
}

// Amélioration UX pour les sélections multiples
document.addEventListener('DOMContentLoaded', function() {
    const multiSelects = document.querySelectorAll('select[multiple]');
    multiSelects.forEach(select => {
        select.addEventListener('change', function() {
            const selectedCount = this.selectedOptions.length;
            const label = this.previousElementSibling;
            if (selectedCount > 0) {
                label.innerHTML = label.textContent.split(' (')[0] + ' (' + selectedCount + ' sélectionné' + (selectedCount > 1 ? 's' : '') + ')';
            } else {
                label.innerHTML = label.textContent.split(' (')[0];
            }
        });
    });
});
</script>

<style>
.modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

.form-label {
    font-weight: 600;
    color: #495057;
}

.border-bottom {
    border-bottom: 2px solid #e9ecef !important;
}

select[multiple] {
    min-height: 120px;
}

.form-check {
    padding: 0.5rem 0;
}

.text-primary {
    color: #0d6efd !important;
}

@media (max-width: 768px) {
    .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    .col-md-6, .col-md-4 {
        margin-bottom: 1rem;
    }
}
</style>
