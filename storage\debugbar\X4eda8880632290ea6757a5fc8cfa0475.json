{"__meta": {"id": "X4eda8880632290ea6757a5fc8cfa0475", "datetime": "2025-07-08 15:47:30", "utime": 1751978850.843265, "method": "POST", "uri": "/livewire/message/etudiant", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751978844.632917, "end": 1751978850.843296, "duration": 6.210379123687744, "duration_str": "6.21s", "measures": [{"label": "Booting", "start": 1751978844.632917, "relative_start": 0, "end": 1751978847.40113, "relative_end": 1751978847.40113, "duration": 2.7682130336761475, "duration_str": "2.77s", "params": [], "collector": null}, {"label": "Application", "start": 1751978847.402519, "relative_start": 2.769602060317993, "end": 1751978850.843299, "relative_end": 2.86102294921875e-06, "duration": 3.4407799243927, "duration_str": "3.44s", "params": [], "collector": null}]}, "memory": {"peak_usage": 27713160, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "livewire.deraq.etudiant.notes (\\resources\\views\\livewire\\deraq\\etudiant\\notes.blade.php)", "param_count": 59, "params": ["parcours", "semestres", "livewireLayout", "errors", "_instance", "showCreateModal", "showEditModal", "showDeleteModal", "showRestoreModal", "viewSupprimesMode", "selectedEtudiantId", "selectedUserId", "etuName", "newUser", "editUser", "editParcours", "query", "filtreParcours", "filtre<PERSON><PERSON>au", "filtreAnnee", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "bulkAction", "totalEtudiants", "totalSupprimés", "selectedItems", "showNotes", "selectedStudentName", "noteUserId", "noteParcourId", "noteMatiereId", "noteTypeId", "noteValeur", "noteObservation", "noteActionMode", "selectedNoteId", "notes", "matieres", "noteTypes", "current_semestre", "current_niveau", "current_annee", "current_parcour", "selectedSemestreId", "viewMode", "searchQuery", "sortBy", "selectedNoteType", "autoSave", "saveStatus", "notesStatistics", "validationErrors", "isValidating", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/etudiant/notes.blade.php&line=0"}, {"name": "components.note-form-modal (\\resources\\views\\components\\note-form-modal.blade.php)", "param_count": 69, "params": ["__env", "app", "errors", "_instance", "parcours", "semestres", "livewireLayout", "showCreateModal", "showEditModal", "showDeleteModal", "showRestoreModal", "viewSupprimesMode", "selectedEtudiantId", "selectedUserId", "etuName", "newUser", "editUser", "editParcours", "query", "filtreParcours", "filtre<PERSON><PERSON>au", "filtreAnnee", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "bulkAction", "totalEtudiants", "totalSupprimés", "selectedItems", "showNotes", "selectedStudentName", "noteUserId", "noteParcourId", "noteMatiereId", "noteTypeId", "noteValeur", "noteObservation", "noteActionMode", "selectedNoteId", "notes", "matieres", "noteTypes", "current_semestre", "current_niveau", "current_annee", "current_parcour", "selectedSemestreId", "viewMode", "searchQuery", "sortBy", "selectedNoteType", "autoSave", "saveStatus", "notesStatistics", "validationErrors", "isValidating", "page", "paginators", "__currentLoopData", "sem", "loop", "type", "__empty_1", "note", "index", "noteClass"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/components/note-form-modal.blade.php&line=0"}, {"name": "components.toast-notifications (\\resources\\views\\components\\toast-notifications.blade.php)", "param_count": 69, "params": ["__env", "app", "errors", "_instance", "parcours", "semestres", "livewireLayout", "showCreateModal", "showEditModal", "showDeleteModal", "showRestoreModal", "viewSupprimesMode", "selectedEtudiantId", "selectedUserId", "etuName", "newUser", "editUser", "editParcours", "query", "filtreParcours", "filtre<PERSON><PERSON>au", "filtreAnnee", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "bulkAction", "totalEtudiants", "totalSupprimés", "selectedItems", "showNotes", "selectedStudentName", "noteUserId", "noteParcourId", "noteMatiereId", "noteTypeId", "noteValeur", "noteObservation", "noteActionMode", "selectedNoteId", "notes", "matieres", "noteTypes", "current_semestre", "current_niveau", "current_annee", "current_parcour", "selectedSemestreId", "viewMode", "searchQuery", "sortBy", "selectedNoteType", "autoSave", "saveStatus", "notesStatistics", "validationErrors", "isValidating", "page", "paginators", "__currentLoopData", "sem", "loop", "type", "__empty_1", "note", "index", "noteClass"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/components/toast-notifications.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 20, "nb_failed_statements": 0, "accumulated_duration": 0.6524499999999999, "accumulated_duration_str": "652ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.007549999999999999, "duration_str": "7.55ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 1.157}, {"sql": "select * from `type_notes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 603}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.030850000000000002, "duration_str": "30.85ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:603", "connection": "imsaaapp", "start_percent": 1.157, "width_percent": 4.728}, {"sql": "select * from `inscription_students` where `user_id` = 109 and `inscription_students`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["109"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 607}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0011200000000000001, "duration_str": "1.12ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:607", "connection": "imsaaapp", "start_percent": 5.886, "width_percent": 0.172}, {"sql": "select * from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 607}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0009, "duration_str": "900μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:607", "connection": "imsaaapp", "start_percent": 6.057, "width_percent": 0.138}, {"sql": "select * from `niveaux` where `niveaux`.`id` in (1) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 607}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:607", "connection": "imsaaapp", "start_percent": 6.195, "width_percent": 0.121}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` in (4) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 607}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0009599999999999999, "duration_str": "960μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:607", "connection": "imsaaapp", "start_percent": 6.316, "width_percent": 0.147}, {"sql": "select * from `parcours` where `parcours`.`id` = 5 and `parcours`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 609}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0008399999999999999, "duration_str": "840μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:609", "connection": "imsaaapp", "start_percent": 6.463, "width_percent": 0.129}, {"sql": "select * from `niveaux` where `niveaux`.`id` = 1 and `niveaux`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 610}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0008, "duration_str": "800μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:610", "connection": "imsaaapp", "start_percent": 6.592, "width_percent": 0.123}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 4 and `annee_universitaires`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 611}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:611", "connection": "imsaaapp", "start_percent": 6.715, "width_percent": 0.12}, {"sql": "select * from `semestres` where `niveau_id` = 1 and `semestres`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 612}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0039, "duration_str": "3.9ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:612", "connection": "imsaaapp", "start_percent": 6.834, "width_percent": 0.598}, {"sql": "select * from `notes` where `user_id` = 109 and exists (select * from `matieres` where `notes`.`matiere_id` = `matieres`.`id` and exists (select * from `ues` where `matieres`.`ue_id` = `ues`.`id` and `parcour_id` = 5 and `niveau_id` = 1 and `annee_universitaire_id` = 4 and `ues`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `notes`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["109", "5", "1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 677}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 614}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.015, "duration_str": "15ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:677", "connection": "imsaaapp", "start_percent": 7.432, "width_percent": 2.299}, {"sql": "select * from `matieres` where `matieres`.`id` in (33, 34, 35, 36, 37, 38, 41, 42, 43, 45, 196, 197, 198, 199, 200, 201, 202, 203) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 677}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 614}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0015, "duration_str": "1.5ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:677", "connection": "imsaaapp", "start_percent": 9.731, "width_percent": 0.23}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (1, 2, 3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 677}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 614}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00074, "duration_str": "740μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:677", "connection": "imsaaapp", "start_percent": 9.961, "width_percent": 0.113}, {"sql": "select count(*) as aggregate from `inscription_students` where exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `inscription_students`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.03603, "duration_str": "36.03ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 10.074, "width_percent": 5.522}, {"sql": "select `id`, `user_id`, `parcour_id`, `niveau_id`, `annee_universitaire_id`, `created_at`, `deleted_at` from `inscription_students` where exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `inscription_students`.`deleted_at` is null order by `created_at` desc limit 10 offset 690", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.13054, "duration_str": "131ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 15.597, "width_percent": 20.008}, {"sql": "select `id`, `nom`, `prenom`, `telephone1`, `sexe`, `photo` from `users` where `users`.`id` in (84, 85, 86, 87, 88, 89, 109, 110, 129, 130) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.15084999999999998, "duration_str": "151ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 35.604, "width_percent": 23.121}, {"sql": "select `id`, `sigle`, `nom` from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.09426999999999999, "duration_str": "94.27ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 58.725, "width_percent": 14.449}, {"sql": "select `id`, `nom` from `niveaux` where `niveaux`.`id` in (1) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.04819, "duration_str": "48.19ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 73.173, "width_percent": 7.386}, {"sql": "select `id`, `nom` from `annee_universitaires` where `annee_universitaires`.`id` in (4) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.07609, "duration_str": "76.09ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 80.559, "width_percent": 11.662}, {"sql": "select `id`, `nom` from `semestres` where `niveau_id` = 1 and `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 204}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.05075, "duration_str": "50.75ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:204", "connection": "imsaaapp", "start_percent": 92.222, "width_percent": 7.778}]}, "models": {"data": {"App\\Models\\Matiere": 18, "App\\Models\\Note": 55, "App\\Models\\Semestre": 3, "App\\Models\\AnneeUniversitaire": 3, "App\\Models\\Niveau": 3, "App\\Models\\Parcour": 3, "App\\Models\\InscriptionStudent": 11, "App\\Models\\TypeNote": 6, "App\\Models\\User": 11}, "count": 113}, "livewire": {"data": {"etudiant #hoJJz2TNBUmyOStN1MHq": "array:5 [\n  \"data\" => array:54 [\n    \"showCreateModal\" => false\n    \"showEditModal\" => false\n    \"showDeleteModal\" => false\n    \"showRestoreModal\" => false\n    \"viewSupprimesMode\" => false\n    \"selectedEtudiantId\" => null\n    \"selectedUserId\" => null\n    \"etuName\" => null\n    \"newUser\" => []\n    \"editUser\" => []\n    \"editParcours\" => []\n    \"query\" => null\n    \"filtreParcours\" => null\n    \"filtreNiveau\" => null\n    \"filtreAnnee\" => null\n    \"perPage\" => 10\n    \"isLoading\" => false\n    \"sortField\" => \"created_at\"\n    \"sortDirection\" => \"desc\"\n    \"showFilters\" => true\n    \"compactView\" => false\n    \"bulkAction\" => \"\"\n    \"totalEtudiants\" => 720\n    \"totalSupprimés\" => 102\n    \"selectedItems\" => []\n    \"showNotes\" => true\n    \"selectedStudentName\" => \"BEHAVANA Gauffit\"\n    \"noteUserId\" => 109\n    \"noteParcourId\" => null\n    \"noteMatiereId\" => null\n    \"noteTypeId\" => null\n    \"noteValeur\" => null\n    \"noteObservation\" => null\n    \"noteActionMode\" => \"list\"\n    \"selectedNoteId\" => null\n    \"notes\" => Illuminate\\Database\\Eloquent\\Collection {#2325\n      #items: array:55 [\n        0 => App\\Models\\Note {#1968\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 10219\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 3\n            \"user_id\" => 109\n            \"matiere_id\" => 202\n            \"history_note_id\" => 679\n            \"created_at\" => \"2023-09-07 16:39:25\"\n            \"updated_at\" => \"2023-09-07 16:39:25\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 10219\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 3\n            \"user_id\" => 109\n            \"matiere_id\" => 202\n            \"history_note_id\" => 679\n            \"created_at\" => \"2023-09-07 16:39:25\"\n            \"updated_at\" => \"2023-09-07 16:39:25\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#2031 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#2039 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        1 => App\\Models\\Note {#1966\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 10179\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 202\n            \"history_note_id\" => 678\n            \"created_at\" => \"2023-09-07 16:39:23\"\n            \"updated_at\" => \"2023-09-07 16:39:23\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 10179\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 202\n            \"history_note_id\" => 678\n            \"created_at\" => \"2023-09-07 16:39:23\"\n            \"updated_at\" => \"2023-09-07 16:39:23\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#2031 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#2002 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        2 => App\\Models\\Note {#1965\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 10139\n            \"valeur\" => 4.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 200\n            \"history_note_id\" => 677\n            \"created_at\" => \"2023-09-07 16:39:02\"\n            \"updated_at\" => \"2023-09-07 16:39:02\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 10139\n            \"valeur\" => 4.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 200\n            \"history_note_id\" => 677\n            \"created_at\" => \"2023-09-07 16:39:02\"\n            \"updated_at\" => \"2023-09-07 16:39:02\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#2029 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#1855 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        3 => App\\Models\\Note {#1964\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 10099\n            \"valeur\" => 4.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 200\n            \"history_note_id\" => 676\n            \"created_at\" => \"2023-09-07 16:39:01\"\n            \"updated_at\" => \"2023-09-07 16:39:01\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 10099\n            \"valeur\" => 4.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 200\n            \"history_note_id\" => 676\n            \"created_at\" => \"2023-09-07 16:39:01\"\n            \"updated_at\" => \"2023-09-07 16:39:01\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#2029 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#2002 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        4 => App\\Models\\Note {#1963\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 10059\n            \"valeur\" => 4.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 200\n            \"history_note_id\" => 675\n            \"created_at\" => \"2023-09-07 16:38:57\"\n            \"updated_at\" => \"2023-09-07 16:38:57\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 10059\n            \"valeur\" => 4.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 200\n            \"history_note_id\" => 675\n            \"created_at\" => \"2023-09-07 16:38:57\"\n            \"updated_at\" => \"2023-09-07 16:38:57\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#2029 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#2002 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        5 => App\\Models\\Note {#1962\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 10019\n            \"valeur\" => 9.5\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 196\n            \"history_note_id\" => 674\n            \"created_at\" => \"2023-09-07 16:38:52\"\n            \"updated_at\" => \"2023-09-07 16:38:52\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 10019\n            \"valeur\" => 9.5\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 196\n            \"history_note_id\" => 674\n            \"created_at\" => \"2023-09-07 16:38:52\"\n            \"updated_at\" => \"2023-09-07 16:38:52\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#2025 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#2002 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        6 => App\\Models\\Note {#1961\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 9979\n            \"valeur\" => 7.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 197\n            \"history_note_id\" => 673\n            \"created_at\" => \"2023-09-07 16:38:49\"\n            \"updated_at\" => \"2023-09-07 16:38:49\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 9979\n            \"valeur\" => 7.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 197\n            \"history_note_id\" => 673\n            \"created_at\" => \"2023-09-07 16:38:49\"\n            \"updated_at\" => \"2023-09-07 16:38:49\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#2026 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#1855 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        7 => App\\Models\\Note {#1960\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 9939\n            \"valeur\" => 7.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 197\n            \"history_note_id\" => 672\n            \"created_at\" => \"2023-09-07 16:38:48\"\n            \"updated_at\" => \"2023-09-07 16:38:48\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 9939\n            \"valeur\" => 7.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 197\n            \"history_note_id\" => 672\n            \"created_at\" => \"2023-09-07 16:38:48\"\n            \"updated_at\" => \"2023-09-07 16:38:48\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#2026 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#2002 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        8 => App\\Models\\Note {#1959\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 9899\n            \"valeur\" => 9.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 198\n            \"history_note_id\" => 671\n            \"created_at\" => \"2023-09-07 16:38:46\"\n            \"updated_at\" => \"2023-09-07 16:38:46\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 9899\n            \"valeur\" => 9.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 198\n            \"history_note_id\" => 671\n            \"created_at\" => \"2023-09-07 16:38:46\"\n            \"updated_at\" => \"2023-09-07 16:38:46\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#2027 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#2002 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        9 => App\\Models\\Note {#1958\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 9859\n            \"valeur\" => 9.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 198\n            \"history_note_id\" => 670\n            \"created_at\" => \"2023-09-07 16:38:45\"\n            \"updated_at\" => \"2023-09-07 16:38:45\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 9859\n            \"valeur\" => 9.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 198\n            \"history_note_id\" => 670\n            \"created_at\" => \"2023-09-07 16:38:45\"\n            \"updated_at\" => \"2023-09-07 16:38:45\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#2027 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#1855 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        10 => App\\Models\\Note {#1957\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 9819\n            \"valeur\" => 12.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 199\n            \"history_note_id\" => 669\n            \"created_at\" => \"2023-09-07 16:38:42\"\n            \"updated_at\" => \"2023-09-07 16:38:42\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 9819\n            \"valeur\" => 12.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 199\n            \"history_note_id\" => 669\n            \"created_at\" => \"2023-09-07 16:38:42\"\n            \"updated_at\" => \"2023-09-07 16:38:42\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#2028 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#1855 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        11 => App\\Models\\Note {#1956\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 9779\n            \"valeur\" => 12.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 199\n            \"history_note_id\" => 668\n            \"created_at\" => \"2023-09-07 16:38:41\"\n            \"updated_at\" => \"2023-09-07 16:38:41\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 9779\n            \"valeur\" => 12.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 199\n            \"history_note_id\" => 668\n            \"created_at\" => \"2023-09-07 16:38:41\"\n            \"updated_at\" => \"2023-09-07 16:38:41\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#2028 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#2002 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        12 => App\\Models\\Note {#1955\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 9739\n            \"valeur\" => 10.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 201\n            \"history_note_id\" => 667\n            \"created_at\" => \"2023-09-07 16:38:38\"\n            \"updated_at\" => \"2023-09-07 16:38:38\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 9739\n            \"valeur\" => 10.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 2\n            \"user_id\" => 109\n            \"matiere_id\" => 201\n            \"history_note_id\" => 667\n            \"created_at\" => \"2023-09-07 16:38:38\"\n            \"updated_at\" => \"2023-09-07 16:38:38\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#2030 …31}\n            \"typeNote\" => App\\Models\\TypeNote {#2002 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        13 => App\\Models\\Note {#1954\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 9699\n            \"valeur\" => 10.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 201\n            \"history_note_id\" => 666\n            \"created_at\" => \"2023-09-07 16:38:36\"\n            \"updated_at\" => \"2023-09-07 16:38:36\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 9699\n            \"valeur\" => 10.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 201\n            \"history_note_id\" => 666\n            \"created_at\" => \"2023-09-07 16:38:36\"\n            \"updated_at\" => \"2023-09-07 16:38:36\"\n             …1\n          ]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        14 => App\\Models\\Note {#1953\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        15 => App\\Models\\Note {#1952\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        16 => App\\Models\\Note {#1951\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        17 => App\\Models\\Note {#1950\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        18 => App\\Models\\Note {#1949\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        19 => App\\Models\\Note {#1948\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        20 => App\\Models\\Note {#1947\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        21 => App\\Models\\Note {#1946\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        22 => App\\Models\\Note {#1945\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        23 => App\\Models\\Note {#1944\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        24 => App\\Models\\Note {#1943\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        25 => App\\Models\\Note {#1942\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        26 => App\\Models\\Note {#1941\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        27 => App\\Models\\Note {#1972\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        28 => App\\Models\\Note {#1973\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        29 => App\\Models\\Note {#1974\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        30 => App\\Models\\Note {#1975\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        31 => App\\Models\\Note {#1976\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        32 => App\\Models\\Note {#1977\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        33 => App\\Models\\Note {#1978\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        34 => App\\Models\\Note {#1979\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        35 => App\\Models\\Note {#1980\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        36 => App\\Models\\Note {#1981\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        37 => App\\Models\\Note {#1982\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        38 => App\\Models\\Note {#1983\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        39 => App\\Models\\Note {#1984\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        40 => App\\Models\\Note {#1985\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        41 => App\\Models\\Note {#1986\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        42 => App\\Models\\Note {#1987\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        43 => App\\Models\\Note {#1988\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        44 => App\\Models\\Note {#1989\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        45 => App\\Models\\Note {#1990\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        46 => App\\Models\\Note {#1991\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        47 => App\\Models\\Note {#1992\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        48 => App\\Models\\Note {#1993\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        49 => App\\Models\\Note {#1994\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        50 => App\\Models\\Note {#1995\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        51 => App\\Models\\Note {#1996\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        52 => App\\Models\\Note {#1997\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        53 => App\\Models\\Note {#1998\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n        54 => App\\Models\\Note {#1999\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [ …10]\n          #original: array:10 [ …10]\n          #changes: []\n          #casts: array:1 [ …1]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [ …2]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [ …6]\n          #guarded: array:1 [ …1]\n          #forceDeleting: false\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"matieres\" => []\n    \"noteTypes\" => Illuminate\\Database\\Eloquent\\Collection {#2294\n      #items: array:3 [\n        0 => App\\Models\\TypeNote {#1793\n          #connection: \"mysql\"\n          #table: \"type_notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [ …2]\n          #original: array:2 [ …2]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [ …1]\n        }\n        1 => App\\Models\\TypeNote {#1699\n          #connection: \"mysql\"\n          #table: \"type_notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [ …2]\n          #original: array:2 [ …2]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [ …1]\n        }\n        2 => App\\Models\\TypeNote {#1698\n          #connection: \"mysql\"\n          #table: \"type_notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [ …2]\n          #original: array:2 [ …2]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [ …1]\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"current_semestre\" => App\\Models\\Semestre {#1892\n      #connection: \"mysql\"\n      #table: \"semestres\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:4 [\n        \"id\" => 1\n        \"nom\" => \"Semestre 1\"\n        \"niveau_id\" => 1\n        \"deleted_at\" => null\n      ]\n      #original: array:4 [\n        \"id\" => 1\n        \"nom\" => \"Semestre 1\"\n        \"niveau_id\" => 1\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: false\n      #hidden: []\n      #visible: []\n      #fillable: array:2 [\n        0 => \"nom\"\n        1 => \"niveau_id\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"current_niveau\" => App\\Models\\Niveau {#1872\n      #connection: \"mysql\"\n      #table: \"niveaux\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:4 [\n        \"id\" => 1\n        \"nom\" => \"1ère année\"\n        \"sigle\" => \"L1\"\n        \"deleted_at\" => null\n      ]\n      #original: array:4 [\n        \"id\" => 1\n        \"nom\" => \"1ère année\"\n        \"sigle\" => \"L1\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"current_annee\" => App\\Models\\AnneeUniversitaire {#1879\n      #connection: \"mysql\"\n      #table: \"annee_universitaires\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:3 [\n        \"id\" => 4\n        \"nom\" => \"2022/2023\"\n        \"deleted_at\" => null\n      ]\n      #original: array:3 [\n        \"id\" => 4\n        \"nom\" => \"2022/2023\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: false\n      #hidden: []\n      #visible: []\n      #fillable: array:1 [\n        0 => \"nom\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"current_parcour\" => App\\Models\\Parcour {#1865\n      #connection: \"mysql\"\n      #table: \"parcours\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 5\n        \"sigle\" => \"TD\"\n        \"nom\" => \"Transit et Douane\"\n        \"mention_id\" => 2\n        \"created_at\" => null\n        \"updated_at\" => null\n        \"deleted_at\" => null\n      ]\n      #original: array:7 [\n        \"id\" => 5\n        \"sigle\" => \"TD\"\n        \"nom\" => \"Transit et Douane\"\n        \"mention_id\" => 2\n        \"created_at\" => null\n        \"updated_at\" => null\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:3 [\n        0 => \"nom\"\n        1 => \"sigle\"\n        2 => \"mention_id\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"selectedSemestreId\" => \"\"\n    \"viewMode\" => \"table\"\n    \"searchQuery\" => \"\"\n    \"sortBy\" => \"created_at_desc\"\n    \"selectedNoteType\" => \"\"\n    \"autoSave\" => true\n    \"saveStatus\" => []\n    \"notesStatistics\" => array:5 [\n      \"average\" => 11.638\n      \"best\" => 18.0\n      \"worst\" => 0.0\n      \"count\" => 55\n      \"success_rate\" => 69.090909090909\n    ]\n    \"validationErrors\" => []\n    \"isValidating\" => false\n    \"page\" => \"70\"\n    \"paginators\" => array:1 [\n      \"page\" => \"70\"\n    ]\n  ]\n  \"name\" => \"etudiant\"\n  \"view\" => \"livewire.deraq.etudiant.notes\"\n  \"component\" => \"App\\Http\\Livewire\\Etudiant\"\n  \"id\" => \"hoJJz2TNBUmyOStN1MHq\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/gestions/etudiants?page=70\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1751977751\n]"}, "request": {"path_info": "/livewire/message/etudiant", "status_code": "<pre class=sf-dump id=sf-dump-1470167480 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1470167480\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-632733445 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-632733445\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2020810100 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">hoJJz2TNBUmyOStN1MHq</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">etudiant</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"18 characters\">gestions/etudiants</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">7f98410f</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:54</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>showCreateModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showEditModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showDeleteModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showRestoreModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>viewSupprimesMode</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>selectedEtudiantId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>selectedUserId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>etuName</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>newUser</span>\" => []\n      \"<span class=sf-dump-key>editUser</span>\" => []\n      \"<span class=sf-dump-key>editParcours</span>\" => []\n      \"<span class=sf-dump-key>query</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filtreParcours</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filtreNiveau</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filtreAnnee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>perPage</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>isLoading</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>sortField</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>sortDirection</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n      \"<span class=sf-dump-key>showFilters</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>compactView</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>bulkAction</span>\" => \"\"\n      \"<span class=sf-dump-key>totalEtudiants</span>\" => <span class=sf-dump-num>720</span>\n      \"<span class=sf-dump-key>totalSupprim&#233;s</span>\" => <span class=sf-dump-num>102</span>\n      \"<span class=sf-dump-key>selectedItems</span>\" => []\n      \"<span class=sf-dump-key>showNotes</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>selectedStudentName</span>\" => \"\"\n      \"<span class=sf-dump-key>noteUserId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>noteParcourId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>noteMatiereId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>noteTypeId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>noteValeur</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>noteObservation</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>noteActionMode</span>\" => \"<span class=sf-dump-str title=\"4 characters\">list</span>\"\n      \"<span class=sf-dump-key>selectedNoteId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>notes</span>\" => []\n      \"<span class=sf-dump-key>matieres</span>\" => []\n      \"<span class=sf-dump-key>noteTypes</span>\" => []\n      \"<span class=sf-dump-key>current_semestre</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>current_niveau</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>current_annee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>current_parcour</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>selectedSemestreId</span>\" => \"\"\n      \"<span class=sf-dump-key>viewMode</span>\" => \"<span class=sf-dump-str title=\"5 characters\">table</span>\"\n      \"<span class=sf-dump-key>searchQuery</span>\" => \"\"\n      \"<span class=sf-dump-key>sortBy</span>\" => \"<span class=sf-dump-str title=\"15 characters\">created_at_desc</span>\"\n      \"<span class=sf-dump-key>selectedNoteType</span>\" => \"\"\n      \"<span class=sf-dump-key>autoSave</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>saveStatus</span>\" => []\n      \"<span class=sf-dump-key>notesStatistics</span>\" => []\n      \"<span class=sf-dump-key>validationErrors</span>\" => []\n      \"<span class=sf-dump-key>isValidating</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">70</span>\"\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">70</span>\"\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">ce6fffc3a0ac5f81f7f2c2ae83443927e85ebc9692bca128daa32593177d9478</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">18jz</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"9 characters\">showNotes</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>109</span>\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-num>5</span>\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-num>1</span>\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-num>4</span>\n          <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">BEHAVANA Gauffit</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2020810100\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-637621708 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1494</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/gestions/etudiants?page=70</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImNkKzBDUzBESHRnTitNVmIyS3Y4Tnc9PSIsInZhbHVlIjoiQmRBYXBNTmhJSk9ENUtxSkR2TERwVnorMjd0Q2hPK2Z3Tks2KzZqN0JVL2pCb0JMZUZoR0pNeFVEQUYxMXlyQUlyUlltRUlEbURXWTdhZ0ZlTEdlc29mby9vY3lFVHRiVVAxK2N3emY5ZjYzeEtxSUZtb0JJOE92ODZuMWdObnIiLCJtYWMiOiJjNzZlNWE2MTVhMDQ2NzdiZjU0NGI1MzE4NDkxYzUxZGI4NzczMGY4YWY3NzE4OTIxZWE1NGFlY2ZjY2M4MjdhIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IlZjWFNjamJqRDJrSHBOMlZLMU5XVlE9PSIsInZhbHVlIjoiLzZqaml6QWd6NmhLcnZRbVkrYzl2WWxSMW9FT3VzaERvVFpBQWE2dzdNSDVMcUdubXJranRSbXdiWFF1dnVSenQ2Z01uZzRXeHFaZ1Bjb0RFNHJuSk96MG1kVzJrbk9QQXM3VzI2RkozeXdsbDlxOUpiRElPVW1DYlR1RkxoWHUiLCJtYWMiOiJiMzkwMzAwNDg4NWJkNGM5YzAwNTkyZTY5YjE0ZTA2NDFjY2U2NDkxMDY5MGE2NjJjOGFjMjEzNzc1MTY0NjZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-637621708\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1909056531 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52339</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/etudiant</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/etudiant</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"36 characters\">/index.php/livewire/message/etudiant</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1494</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1494</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/gestions/etudiants?page=70</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImNkKzBDUzBESHRnTitNVmIyS3Y4Tnc9PSIsInZhbHVlIjoiQmRBYXBNTmhJSk9ENUtxSkR2TERwVnorMjd0Q2hPK2Z3Tks2KzZqN0JVL2pCb0JMZUZoR0pNeFVEQUYxMXlyQUlyUlltRUlEbURXWTdhZ0ZlTEdlc29mby9vY3lFVHRiVVAxK2N3emY5ZjYzeEtxSUZtb0JJOE92ODZuMWdObnIiLCJtYWMiOiJjNzZlNWE2MTVhMDQ2NzdiZjU0NGI1MzE4NDkxYzUxZGI4NzczMGY4YWY3NzE4OTIxZWE1NGFlY2ZjY2M4MjdhIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IlZjWFNjamJqRDJrSHBOMlZLMU5XVlE9PSIsInZhbHVlIjoiLzZqaml6QWd6NmhLcnZRbVkrYzl2WWxSMW9FT3VzaERvVFpBQWE2dzdNSDVMcUdubXJranRSbXdiWFF1dnVSenQ2Z01uZzRXeHFaZ1Bjb0RFNHJuSk96MG1kVzJrbk9QQXM3VzI2RkozeXdsbDlxOUpiRElPVW1DYlR1RkxoWHUiLCJtYWMiOiJiMzkwMzAwNDg4NWJkNGM5YzAwNTkyZTY5YjE0ZTA2NDFjY2U2NDkxMDY5MGE2NjJjOGFjMjEzNzc1MTY0NjZhIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751978844.6329</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751978844</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1909056531\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-993806017 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IC6ylzyOLsmp8eqEPKRtYbNYwxNcQ3yBFxnhfM9e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-993806017\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1113115502 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 08 Jul 2025 12:47:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im9oa1pyN1FGY0I1TjNudnM5TWZNd3c9PSIsInZhbHVlIjoib0NyYkNkeTlVcmNnZTREbks0Tk5sNDFqVXRYdnlmeXpZNmUzY2pzdnA2cDJBLzJrQW1IeklYR1huV2V4b3B5S1hWOEg3RVZsQlZoOVRXZFhyaHY4U0xpaExrSEpzRVVXQW1uc1VDWmV6Sll6NXVhSDhOa2ZuKzVpdXFyMlNSVlgiLCJtYWMiOiJlMDk2NzUxMWQ5YTllNGI3NDcyOWZhYWNhNGE2OGRjOGFjOWM1MTU5MDJkZDJlN2E4ZDBhZDhlOWY3YjNlYWQ3IiwidGFnIjoiIn0%3D; expires=Tue, 08 Jul 2025 14:47:30 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6IkZtTkIvMXUxcGQzSXVyQkw5VjNWRGc9PSIsInZhbHVlIjoiYktNLzFRcmgvYUpBN2xnN2ZRSXozT3BURWk0S0dVZGdjM1NxTmJtY0ptK01kbVBGbzJqbm1CRFR1TlhLTEF6Z2NvSkQrWWRjcStMNVg3WW14VzFhZFFyWnV0SUZjck5ZRlF5N0UxaE0zZzJqMmVVaEhTbkw5bkcySmFtMkFkYWEiLCJtYWMiOiJhYzlhZDc4ZWVkNDVhMDFmNjhiNjE0ZjBjYzIyZjdmMDQxMWI2N2IxZjdhMjM5YWIwZDgwMGFhNDEzZjI5OWQ4IiwidGFnIjoiIn0%3D; expires=Tue, 08 Jul 2025 14:47:30 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im9oa1pyN1FGY0I1TjNudnM5TWZNd3c9PSIsInZhbHVlIjoib0NyYkNkeTlVcmNnZTREbks0Tk5sNDFqVXRYdnlmeXpZNmUzY2pzdnA2cDJBLzJrQW1IeklYR1huV2V4b3B5S1hWOEg3RVZsQlZoOVRXZFhyaHY4U0xpaExrSEpzRVVXQW1uc1VDWmV6Sll6NXVhSDhOa2ZuKzVpdXFyMlNSVlgiLCJtYWMiOiJlMDk2NzUxMWQ5YTllNGI3NDcyOWZhYWNhNGE2OGRjOGFjOWM1MTU5MDJkZDJlN2E4ZDBhZDhlOWY3YjNlYWQ3IiwidGFnIjoiIn0%3D; expires=Tue, 08-Jul-2025 14:47:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6IkZtTkIvMXUxcGQzSXVyQkw5VjNWRGc9PSIsInZhbHVlIjoiYktNLzFRcmgvYUpBN2xnN2ZRSXozT3BURWk0S0dVZGdjM1NxTmJtY0ptK01kbVBGbzJqbm1CRFR1TlhLTEF6Z2NvSkQrWWRjcStMNVg3WW14VzFhZFFyWnV0SUZjck5ZRlF5N0UxaE0zZzJqMmVVaEhTbkw5bkcySmFtMkFkYWEiLCJtYWMiOiJhYzlhZDc4ZWVkNDVhMDFmNjhiNjE0ZjBjYzIyZjdmMDQxMWI2N2IxZjdhMjM5YWIwZDgwMGFhNDEzZjI5OWQ4IiwidGFnIjoiIn0%3D; expires=Tue, 08-Jul-2025 14:47:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1113115502\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1997795296 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/gestions/etudiants?page=70</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1751977751</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1997795296\", {\"maxDepth\":0})</script>\n"}}