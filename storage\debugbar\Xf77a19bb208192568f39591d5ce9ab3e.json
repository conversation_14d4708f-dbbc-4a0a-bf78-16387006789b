{"__meta": {"id": "Xf77a19bb208192568f39591d5ce9ab3e", "datetime": "2025-07-08 15:48:11", "utime": 1751978891.895137, "method": "POST", "uri": "/livewire/message/etudiant", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751978889.454093, "end": 1751978891.895168, "duration": 2.441075086593628, "duration_str": "2.44s", "measures": [{"label": "Booting", "start": 1751978889.454093, "relative_start": 0, "end": 1751978890.593371, "relative_end": 1751978890.593371, "duration": 1.1392779350280762, "duration_str": "1.14s", "params": [], "collector": null}, {"label": "Application", "start": 1751978890.594224, "relative_start": 1.1401309967041016, "end": 1751978891.895171, "relative_end": 2.86102294921875e-06, "duration": 1.3009469509124756, "duration_str": "1.3s", "params": [], "collector": null}]}, "memory": {"peak_usage": 27413800, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "livewire.deraq.etudiant.index (\\resources\\views\\livewire\\deraq\\etudiant\\index.blade.php)", "param_count": 61, "params": ["etudiants", "parcours", "annees", "niveaux", "livewireLayout", "errors", "_instance", "showCreateModal", "showEditModal", "showDeleteModal", "showRestoreModal", "viewSupprimesMode", "selectedEtudiantId", "selectedUserId", "etuName", "newUser", "editUser", "editParcours", "query", "filtreParcours", "filtre<PERSON><PERSON>au", "filtreAnnee", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "bulkAction", "totalEtudiants", "totalSupprimés", "selectedItems", "showNotes", "selectedStudentName", "noteUserId", "noteParcourId", "noteMatiereId", "noteTypeId", "noteValeur", "noteObservation", "noteActionMode", "selectedNoteId", "notes", "matieres", "noteTypes", "current_semestre", "current_niveau", "current_annee", "current_parcour", "selectedSemestreId", "viewMode", "searchQuery", "sortBy", "selectedNoteType", "autoSave", "saveStatus", "notesStatistics", "validationErrors", "isValidating", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/etudiant/index.blade.php&line=0"}, {"name": "livewire::bootstrap (\\vendor\\livewire\\livewire\\src\\views\\pagination\\bootstrap.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\views\\pagination/bootstrap.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 16, "nb_failed_statements": 0, "accumulated_duration": 0.4033999999999999, "accumulated_duration_str": "403ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0058200000000000005, "duration_str": "5.82ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 1.443}, {"sql": "select * from `notes` where `notes`.`id` in (10139, 9979, 9859, 9819, 9699, 8480, 8415, 8302, 4461, 4180, 2677, 1625, 1511, 1397, 1295, 1163, 1106, 885)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 169}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00132, "duration_str": "1.32ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:80", "connection": "imsaaapp", "start_percent": 1.443, "width_percent": 0.327}, {"sql": "select * from `matieres` where `matieres`.`id` in (33, 34, 35, 36, 37, 38, 41, 42, 43, 45, 196, 197, 198, 199, 200, 201, 202, 203) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 175}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php", "line": 13}, {"index": 25, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php", "line": 18}], "duration": 0.00111, "duration_str": "1.11ms", "stmt_id": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php:175", "connection": "imsaaapp", "start_percent": 1.77, "width_percent": 0.275}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 175}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php", "line": 13}, {"index": 25, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php", "line": 18}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php:175", "connection": "imsaaapp", "start_percent": 2.045, "width_percent": 0.178}, {"sql": "select * from `matieres` where `matieres`.`id` in (33, 34, 35, 36, 37, 38, 41, 42, 43, 45)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 169}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.001, "duration_str": "1ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:80", "connection": "imsaaapp", "start_percent": 2.224, "width_percent": 0.248}, {"sql": "select * from `type_notes` where `type_notes`.`id` in (1, 2, 3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 80}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 60}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 169}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:80", "connection": "imsaaapp", "start_percent": 2.471, "width_percent": 0.245}, {"sql": "select * from `semestres` where `semestres`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 2.717, "width_percent": 0.174}, {"sql": "select * from `niveaux` where `niveaux`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 2.89, "width_percent": 0.174}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.0008, "duration_str": "800μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 3.064, "width_percent": 0.198}, {"sql": "select * from `parcours` where `parcours`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 151}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}], "duration": 0.0009, "duration_str": "900μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php:108", "connection": "imsaaapp", "start_percent": 3.262, "width_percent": 0.223}, {"sql": "select count(*) as aggregate from `inscription_students` where exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `inscription_students`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.008150000000000001, "duration_str": "8.15ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 3.485, "width_percent": 2.02}, {"sql": "select `id`, `user_id`, `parcour_id`, `niveau_id`, `annee_universitaire_id`, `created_at`, `deleted_at` from `inscription_students` where exists (select * from `users` where `inscription_students`.`user_id` = `users`.`id` and exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and `users`.`deleted_at` is null) and `inscription_students`.`deleted_at` is null order by `created_at` desc limit 10 offset 690", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.37764, "duration_str": "378ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 5.506, "width_percent": 93.614}, {"sql": "select `id`, `nom`, `prenom`, `telephone1`, `sexe`, `photo` from `users` where `users`.`id` in (84, 85, 86, 87, 88, 89, 109, 110, 129, 130) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 99.12, "width_percent": 0.231}, {"sql": "select `id`, `sigle`, `nom` from `parcours` where `parcours`.`id` in (5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 99.351, "width_percent": 0.174}, {"sql": "select `id`, `nom` from `niveaux` where `niveaux`.`id` in (1) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00126, "duration_str": "1.26ms", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 99.524, "width_percent": 0.312}, {"sql": "select `id`, `nom` from `annee_universitaires` where `annee_universitaires`.`id` in (4) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Etudiant.php", "line": 191}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00066, "duration_str": "660μs", "stmt_id": "\\app\\Http\\Livewire\\Etudiant.php:191", "connection": "imsaaapp", "start_percent": 99.836, "width_percent": 0.164}]}, "models": {"data": {"App\\Models\\InscriptionStudent": 10, "App\\Models\\Parcour": 2, "App\\Models\\AnneeUniversitaire": 2, "App\\Models\\Niveau": 2, "App\\Models\\Semestre": 1, "App\\Models\\TypeNote": 4, "App\\Models\\Matiere": 28, "App\\Models\\Note": 18, "App\\Models\\User": 11}, "count": 78}, "livewire": {"data": {"etudiant #hoJJz2TNBUmyOStN1MHq": "array:5 [\n  \"data\" => array:54 [\n    \"showCreateModal\" => false\n    \"showEditModal\" => false\n    \"showDeleteModal\" => false\n    \"showRestoreModal\" => false\n    \"viewSupprimesMode\" => false\n    \"selectedEtudiantId\" => null\n    \"selectedUserId\" => null\n    \"etuName\" => null\n    \"newUser\" => []\n    \"editUser\" => []\n    \"editParcours\" => []\n    \"query\" => null\n    \"filtreParcours\" => null\n    \"filtreNiveau\" => null\n    \"filtreAnnee\" => null\n    \"perPage\" => 10\n    \"isLoading\" => false\n    \"sortField\" => \"created_at\"\n    \"sortDirection\" => \"desc\"\n    \"showFilters\" => true\n    \"compactView\" => false\n    \"bulkAction\" => \"\"\n    \"totalEtudiants\" => 720\n    \"totalSupprimés\" => 102\n    \"selectedItems\" => []\n    \"showNotes\" => false\n    \"selectedStudentName\" => \"BEHAVANA Gauffit\"\n    \"noteUserId\" => 109\n    \"noteParcourId\" => null\n    \"noteMatiereId\" => null\n    \"noteTypeId\" => null\n    \"noteValeur\" => null\n    \"noteObservation\" => null\n    \"noteActionMode\" => \"edit\"\n    \"selectedNoteId\" => null\n    \"notes\" => Illuminate\\Database\\Eloquent\\Collection {#2181\n      #items: array:18 [\n        0 => App\\Models\\Note {#1451\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 10139\n            \"valeur\" => 4.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 200\n            \"history_note_id\" => 677\n            \"created_at\" => \"2023-09-07 16:39:02\"\n            \"updated_at\" => \"2023-09-07 16:39:02\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 10139\n            \"valeur\" => 4.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 200\n            \"history_note_id\" => 677\n            \"created_at\" => \"2023-09-07 16:39:02\"\n            \"updated_at\" => \"2023-09-07 16:39:02\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1782\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 200\n                \"nom\" => \"ITALIEN\"\n                \"code\" => \"ITALI\"\n                \"syllabus\" => null\n                \"user_id\" => 180\n                \"ue_id\" => 162\n                \"created_at\" => \"2023-09-07 13:10:29\"\n                \"updated_at\" => \"2023-09-07 13:10:29\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #original: array:10 [\n                \"id\" => 200\n                \"nom\" => \"ITALIEN\"\n                \"code\" => \"ITALI\"\n                \"syllabus\" => null\n                \"user_id\" => 180\n                \"ue_id\" => 162\n                \"created_at\" => \"2023-09-07 13:10:29\"\n                \"updated_at\" => \"2023-09-07 13:10:29\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1665\n              #connection: \"mysql\"\n              #table: \"type_notes\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:2 [\n                \"id\" => 1\n                \"nom\" => \"Partiel 1\"\n              ]\n              #original: array:2 [\n                \"id\" => 1\n                \"nom\" => \"Partiel 1\"\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n            }\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        1 => App\\Models\\Note {#706\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 9979\n            \"valeur\" => 7.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 197\n            \"history_note_id\" => 673\n            \"created_at\" => \"2023-09-07 16:38:49\"\n            \"updated_at\" => \"2023-09-07 16:38:49\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 9979\n            \"valeur\" => 7.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 197\n            \"history_note_id\" => 673\n            \"created_at\" => \"2023-09-07 16:38:49\"\n            \"updated_at\" => \"2023-09-07 16:38:49\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1779\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 197\n                \"nom\" => \"GESTION DE PROJET \"\n                \"code\" => \"GESPROJ\"\n                \"syllabus\" => null\n                \"user_id\" => 157\n                \"ue_id\" => 160\n                \"created_at\" => \"2023-09-07 13:07:18\"\n                \"updated_at\" => \"2023-09-07 13:07:18\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #original: array:10 [\n                \"id\" => 197\n                \"nom\" => \"GESTION DE PROJET \"\n                \"code\" => \"GESPROJ\"\n                \"syllabus\" => null\n                \"user_id\" => 157\n                \"ue_id\" => 160\n                \"created_at\" => \"2023-09-07 13:07:18\"\n                \"updated_at\" => \"2023-09-07 13:07:18\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1665}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        2 => App\\Models\\Note {#718\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 9859\n            \"valeur\" => 9.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 198\n            \"history_note_id\" => 670\n            \"created_at\" => \"2023-09-07 16:38:45\"\n            \"updated_at\" => \"2023-09-07 16:38:45\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 9859\n            \"valeur\" => 9.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 198\n            \"history_note_id\" => 670\n            \"created_at\" => \"2023-09-07 16:38:45\"\n            \"updated_at\" => \"2023-09-07 16:38:45\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1780\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 198\n                \"nom\" => \"TRANSIT ET DOUANE\"\n                \"code\" => \"TRANSDOU\"\n                \"syllabus\" => null\n                \"user_id\" => 158\n                \"ue_id\" => 161\n                \"created_at\" => \"2023-09-07 13:08:36\"\n                \"updated_at\" => \"2023-09-07 13:08:36\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #original: array:10 [\n                \"id\" => 198\n                \"nom\" => \"TRANSIT ET DOUANE\"\n                \"code\" => \"TRANSDOU\"\n                \"syllabus\" => null\n                \"user_id\" => 158\n                \"ue_id\" => 161\n                \"created_at\" => \"2023-09-07 13:08:36\"\n                \"updated_at\" => \"2023-09-07 13:08:36\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1665}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        3 => App\\Models\\Note {#1452\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 9819\n            \"valeur\" => 12.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 199\n            \"history_note_id\" => 669\n            \"created_at\" => \"2023-09-07 16:38:42\"\n            \"updated_at\" => \"2023-09-07 16:38:42\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 9819\n            \"valeur\" => 12.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 199\n            \"history_note_id\" => 669\n            \"created_at\" => \"2023-09-07 16:38:42\"\n            \"updated_at\" => \"2023-09-07 16:38:42\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1781\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 199\n                \"nom\" => \"DROIT  DE TRANSPORT \"\n                \"code\" => \"DRTRANSP\"\n                \"syllabus\" => null\n                \"user_id\" => 154\n                \"ue_id\" => 161\n                \"created_at\" => \"2023-09-07 13:09:16\"\n                \"updated_at\" => \"2023-09-07 13:09:16\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #original: array:10 [\n                \"id\" => 199\n                \"nom\" => \"DROIT  DE TRANSPORT \"\n                \"code\" => \"DRTRANSP\"\n                \"syllabus\" => null\n                \"user_id\" => 154\n                \"ue_id\" => 161\n                \"created_at\" => \"2023-09-07 13:09:16\"\n                \"updated_at\" => \"2023-09-07 13:09:16\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1665}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        4 => App\\Models\\Note {#1453\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 9699\n            \"valeur\" => 10.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 201\n            \"history_note_id\" => 666\n            \"created_at\" => \"2023-09-07 16:38:36\"\n            \"updated_at\" => \"2023-09-07 16:38:36\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 9699\n            \"valeur\" => 10.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 201\n            \"history_note_id\" => 666\n            \"created_at\" => \"2023-09-07 16:38:36\"\n            \"updated_at\" => \"2023-09-07 16:38:36\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1783\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 201\n                \"nom\" => \"MARKETING\"\n                \"code\" => \"MARKT\"\n                \"syllabus\" => null\n                \"user_id\" => 157\n                \"ue_id\" => 162\n                \"created_at\" => \"2023-09-07 13:10:57\"\n                \"updated_at\" => \"2023-09-07 13:10:57\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #original: array:10 [\n                \"id\" => 201\n                \"nom\" => \"MARKETING\"\n                \"code\" => \"MARKT\"\n                \"syllabus\" => null\n                \"user_id\" => 157\n                \"ue_id\" => 162\n                \"created_at\" => \"2023-09-07 13:10:57\"\n                \"updated_at\" => \"2023-09-07 13:10:57\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1665}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        5 => App\\Models\\Note {#708\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 8480\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 202\n            \"history_note_id\" => 589\n            \"created_at\" => \"2023-09-07 15:25:08\"\n            \"updated_at\" => \"2023-09-07 15:25:08\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 8480\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 202\n            \"history_note_id\" => 589\n            \"created_at\" => \"2023-09-07 15:25:08\"\n            \"updated_at\" => \"2023-09-07 15:25:08\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1784\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 202\n                \"nom\" => \"PROJET TUTORET \"\n                \"code\" => \"PROJTUTO\"\n                \"syllabus\" => null\n                \"user_id\" => 146\n                \"ue_id\" => 163\n                \"created_at\" => \"2023-09-07 13:11:56\"\n                \"updated_at\" => \"2023-09-07 13:11:56\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #original: array:10 [\n                \"id\" => 202\n                \"nom\" => \"PROJET TUTORET \"\n                \"code\" => \"PROJTUTO\"\n                \"syllabus\" => null\n                \"user_id\" => 146\n                \"ue_id\" => 163\n                \"created_at\" => \"2023-09-07 13:11:56\"\n                \"updated_at\" => \"2023-09-07 13:11:56\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1665}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        6 => App\\Models\\Note {#676\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 8415\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 203\n            \"history_note_id\" => 587\n            \"created_at\" => \"2023-09-07 15:10:52\"\n            \"updated_at\" => \"2023-09-07 15:10:52\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 8415\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 203\n            \"history_note_id\" => 587\n            \"created_at\" => \"2023-09-07 15:10:52\"\n            \"updated_at\" => \"2023-09-07 15:10:52\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1785\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 203\n                \"nom\" => \"VISITE D'ENTREPRISE \"\n                \"code\" => \"VISI\"\n                \"syllabus\" => null\n                \"user_id\" => 147\n                \"ue_id\" => 163\n                \"created_at\" => \"2023-09-07 13:12:34\"\n                \"updated_at\" => \"2023-09-07 13:12:34\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #original: array:10 [\n                \"id\" => 203\n                \"nom\" => \"VISITE D'ENTREPRISE \"\n                \"code\" => \"VISI\"\n                \"syllabus\" => null\n                \"user_id\" => 147\n                \"ue_id\" => 163\n                \"created_at\" => \"2023-09-07 13:12:34\"\n                \"updated_at\" => \"2023-09-07 13:12:34\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1665}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        7 => App\\Models\\Note {#678\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 8302\n            \"valeur\" => 10.25\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 196\n            \"history_note_id\" => 582\n            \"created_at\" => \"2023-09-07 15:04:26\"\n            \"updated_at\" => \"2023-09-07 15:04:26\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 8302\n            \"valeur\" => 10.25\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 196\n            \"history_note_id\" => 582\n            \"created_at\" => \"2023-09-07 15:04:26\"\n            \"updated_at\" => \"2023-09-07 15:04:26\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1778\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 196\n                \"nom\" => \"COMPTABILITE\"\n                \"code\" => \"COMP\"\n                \"syllabus\" => null\n                \"user_id\" => 155\n                \"ue_id\" => 160\n                \"created_at\" => \"2023-09-07 13:06:35\"\n                \"updated_at\" => \"2023-09-07 13:06:35\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #original: array:10 [\n                \"id\" => 196\n                \"nom\" => \"COMPTABILITE\"\n                \"code\" => \"COMP\"\n                \"syllabus\" => null\n                \"user_id\" => 155\n                \"ue_id\" => 160\n                \"created_at\" => \"2023-09-07 13:06:35\"\n                \"updated_at\" => \"2023-09-07 13:06:35\"\n                \"deleted_at\" => null\n                \"is_filled\" => 0\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1665}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        8 => App\\Models\\Note {#701\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 4461\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 42\n            \"history_note_id\" => 265\n            \"created_at\" => \"2023-06-14 11:15:39\"\n            \"updated_at\" => \"2023-06-14 11:15:39\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 4461\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 42\n            \"history_note_id\" => 265\n            \"created_at\" => \"2023-06-14 11:15:39\"\n            \"updated_at\" => \"2023-06-14 11:15:39\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1775\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 42\n                \"nom\" => \"VISITE D'ENTREPRISE \"\n                \"code\" => \"VISENT\"\n                \"syllabus\" => null\n                \"user_id\" => 147\n                \"ue_id\" => 93\n                \"created_at\" => \"2023-06-07 16:38:36\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #original: array:10 [\n                \"id\" => 42\n                \"nom\" => \"VISITE D'ENTREPRISE \"\n                \"code\" => \"VISENT\"\n                \"syllabus\" => null\n                \"user_id\" => 147\n                \"ue_id\" => 93\n                \"created_at\" => \"2023-06-07 16:38:36\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1665}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        9 => App\\Models\\Note {#702\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 4180\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 41\n            \"history_note_id\" => 246\n            \"created_at\" => \"2023-06-14 10:37:15\"\n            \"updated_at\" => \"2023-06-14 10:37:15\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 4180\n            \"valeur\" => 15.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 41\n            \"history_note_id\" => 246\n            \"created_at\" => \"2023-06-14 10:37:15\"\n            \"updated_at\" => \"2023-06-14 10:37:15\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1774\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 41\n                \"nom\" => \"INFORMATIQUE BUREAUTIQUE \"\n                \"code\" => \"INFOR\"\n                \"syllabus\" => null\n                \"user_id\" => 145\n                \"ue_id\" => 93\n                \"created_at\" => \"2023-06-07 16:37:48\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #original: array:10 [\n                \"id\" => 41\n                \"nom\" => \"INFORMATIQUE BUREAUTIQUE \"\n                \"code\" => \"INFOR\"\n                \"syllabus\" => null\n                \"user_id\" => 145\n                \"ue_id\" => 93\n                \"created_at\" => \"2023-06-07 16:37:48\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1665}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        10 => App\\Models\\Note {#679\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 2677\n            \"valeur\" => 17.22\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 34\n            \"history_note_id\" => 146\n            \"created_at\" => \"2023-06-13 07:32:43\"\n            \"updated_at\" => \"2023-06-13 07:32:43\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 2677\n            \"valeur\" => 17.22\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 34\n            \"history_note_id\" => 146\n            \"created_at\" => \"2023-06-13 07:32:43\"\n            \"updated_at\" => \"2023-06-13 07:32:43\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1769\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 34\n                \"nom\" => \"ITALIEN\"\n                \"code\" => \"TDL1ITA\"\n                \"syllabus\" => null\n                \"user_id\" => 180\n                \"ue_id\" => 90\n                \"created_at\" => \"2023-06-07 16:33:42\"\n                \"updated_at\" => \"2024-02-20 15:20:00\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #original: array:10 [\n                \"id\" => 34\n                \"nom\" => \"ITALIEN\"\n                \"code\" => \"TDL1ITA\"\n                \"syllabus\" => null\n                \"user_id\" => 180\n                \"ue_id\" => 90\n                \"created_at\" => \"2023-06-07 16:33:42\"\n                \"updated_at\" => \"2024-02-20 15:20:00\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1665}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        11 => App\\Models\\Note {#670\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 1625\n            \"valeur\" => 10.5\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 38\n            \"history_note_id\" => 64\n            \"created_at\" => \"2023-06-09 10:40:48\"\n            \"updated_at\" => \"2023-06-09 10:40:48\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 1625\n            \"valeur\" => 10.5\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 38\n            \"history_note_id\" => 64\n            \"created_at\" => \"2023-06-09 10:40:48\"\n            \"updated_at\" => \"2023-06-09 10:40:48\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1773\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 38\n                \"nom\" => \"GÉOGRAPHIE DES ECHANGES \"\n                \"code\" => \"GEOECH\"\n                \"syllabus\" => null\n                \"user_id\" => 159\n                \"ue_id\" => 92\n                \"created_at\" => \"2023-06-07 16:36:45\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #original: array:10 [\n                \"id\" => 38\n                \"nom\" => \"GÉOGRAPHIE DES ECHANGES \"\n                \"code\" => \"GEOECH\"\n                \"syllabus\" => null\n                \"user_id\" => 159\n                \"ue_id\" => 92\n                \"created_at\" => \"2023-06-07 16:36:45\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1665}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        12 => App\\Models\\Note {#669\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 1511\n            \"valeur\" => 13.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 35\n            \"history_note_id\" => 62\n            \"created_at\" => \"2023-06-09 10:36:51\"\n            \"updated_at\" => \"2023-06-09 10:36:51\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 1511\n            \"valeur\" => 13.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 35\n            \"history_note_id\" => 62\n            \"created_at\" => \"2023-06-09 10:36:51\"\n            \"updated_at\" => \"2023-06-09 10:36:51\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1770\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 35\n                \"nom\" => \"LOGISTIQUE \"\n                \"code\" => \"LOG\"\n                \"syllabus\" => null\n                \"user_id\" => 158\n                \"ue_id\" => 91\n                \"created_at\" => \"2023-06-07 16:34:24\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #original: array:10 [\n                \"id\" => 35\n                \"nom\" => \"LOGISTIQUE \"\n                \"code\" => \"LOG\"\n                \"syllabus\" => null\n                \"user_id\" => 158\n                \"ue_id\" => 91\n                \"created_at\" => \"2023-06-07 16:34:24\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1665}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        13 => App\\Models\\Note {#1724\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 1397\n            \"valeur\" => 17.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 36\n            \"history_note_id\" => 60\n            \"created_at\" => \"2023-06-09 10:27:16\"\n            \"updated_at\" => \"2023-06-09 10:27:16\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 1397\n            \"valeur\" => 17.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 36\n            \"history_note_id\" => 60\n            \"created_at\" => \"2023-06-09 10:27:16\"\n            \"updated_at\" => \"2023-06-09 10:27:16\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1771\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 36\n                \"nom\" => \"ORGANISATION D'ENTREPRISE \"\n                \"code\" => \"ORGAE\"\n                \"syllabus\" => null\n                \"user_id\" => 148\n                \"ue_id\" => 91\n                \"created_at\" => \"2023-06-07 16:35:04\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #original: array:10 [\n                \"id\" => 36\n                \"nom\" => \"ORGANISATION D'ENTREPRISE \"\n                \"code\" => \"ORGAE\"\n                \"syllabus\" => null\n                \"user_id\" => 148\n                \"ue_id\" => 91\n                \"created_at\" => \"2023-06-07 16:35:04\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1665}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        14 => App\\Models\\Note {#1721\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 1295\n            \"valeur\" => 12.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 37\n            \"history_note_id\" => 56\n            \"created_at\" => \"2023-06-09 10:17:34\"\n            \"updated_at\" => \"2023-06-09 10:17:34\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 1295\n            \"valeur\" => 12.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 37\n            \"history_note_id\" => 56\n            \"created_at\" => \"2023-06-09 10:17:34\"\n            \"updated_at\" => \"2023-06-09 10:17:34\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1772\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 37\n                \"nom\" => \"TECHNIQUE DU COMMERCE INTERNATIONAL \"\n                \"code\" => \"TECHNACOMI\"\n                \"syllabus\" => null\n                \"user_id\" => 157\n                \"ue_id\" => 92\n                \"created_at\" => \"2023-06-07 16:36:04\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #original: array:10 [\n                \"id\" => 37\n                \"nom\" => \"TECHNIQUE DU COMMERCE INTERNATIONAL \"\n                \"code\" => \"TECHNACOMI\"\n                \"syllabus\" => null\n                \"user_id\" => 157\n                \"ue_id\" => 92\n                \"created_at\" => \"2023-06-07 16:36:04\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #changes: []\n              #casts: array:1 [\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [\n                0 => \"nom\"\n                1 => \"syllabus\"\n                2 => \"user_id\"\n                3 => \"ue_id\"\n                4 => \"parcour_id\"\n                5 => \"niveau_id\"\n                6 => \"code\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1665}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        15 => App\\Models\\Note {#1720\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 1163\n            \"valeur\" => 11.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 45\n            \"history_note_id\" => 51\n            \"created_at\" => \"2023-06-09 10:03:14\"\n            \"updated_at\" => \"2023-06-09 10:03:14\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 1163\n            \"valeur\" => 11.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 45\n            \"history_note_id\" => 51\n            \"created_at\" => \"2023-06-09 10:03:14\"\n            \"updated_at\" => \"2023-06-09 10:03:14\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1777\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 45\n                \"nom\" => \"MATHÉMATIQUE FINANCIÈRE\"\n                \"code\" => \"MATHFIN\"\n                \"syllabus\" => null\n                \"user_id\" => 155\n                \"ue_id\" => 94\n                \"created_at\" => \"2023-06-07 16:40:18\"\n                \"updated_at\" => \"2023-10-26 17:36:03\"\n                \"deleted_at\" => null\n                \"is_filled\" => 1\n              ]\n              #original: array:10 [ …10]\n              #changes: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [ …7]\n              #guarded: array:1 [ …1]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1665}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        16 => App\\Models\\Note {#1718\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 1106\n            \"valeur\" => 0.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 43\n            \"history_note_id\" => 50\n            \"created_at\" => \"2023-06-09 09:59:29\"\n            \"updated_at\" => \"2023-06-09 09:59:29\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 1106\n            \"valeur\" => 0.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 43\n            \"history_note_id\" => 50\n            \"created_at\" => \"2023-06-09 09:59:29\"\n            \"updated_at\" => \"2023-06-09 09:59:29\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1776\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [ …10]\n              #original: array:10 [ …10]\n              #changes: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [ …7]\n              #guarded: array:1 [ …1]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1665}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        17 => App\\Models\\Note {#1716\n          #connection: \"mysql\"\n          #table: \"notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:10 [\n            \"id\" => 885\n            \"valeur\" => 16.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 33\n            \"history_note_id\" => 45\n            \"created_at\" => \"2023-06-09 07:59:22\"\n            \"updated_at\" => \"2023-06-09 07:59:22\"\n            \"deleted_at\" => null\n          ]\n          #original: array:10 [\n            \"id\" => 885\n            \"valeur\" => 16.0\n            \"observation\" => \"\"\n            \"type_note_id\" => 1\n            \"user_id\" => 109\n            \"matiere_id\" => 33\n            \"history_note_id\" => 45\n            \"created_at\" => \"2023-06-09 07:59:22\"\n            \"updated_at\" => \"2023-06-09 07:59:22\"\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:2 [\n            \"matiere\" => App\\Models\\Matiere {#1767\n              #connection: \"mysql\"\n              #table: \"matieres\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [ …10]\n              #original: array:10 [ …10]\n              #changes: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dates: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              #hidden: []\n              #visible: []\n              #fillable: array:7 [ …7]\n              #guarded: array:1 [ …1]\n              #forceDeleting: false\n            }\n            \"typeNote\" => App\\Models\\TypeNote {#1665}\n          ]\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"valeur\"\n            1 => \"type_note_id\"\n            2 => \"user_id\"\n            3 => \"matiere_id\"\n            4 => \"history_note_id\"\n            5 => \"observation\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"matieres\" => []\n    \"noteTypes\" => Illuminate\\Database\\Eloquent\\Collection {#2178\n      #items: array:3 [\n        0 => App\\Models\\TypeNote {#1706\n          #connection: \"mysql\"\n          #table: \"type_notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [\n            \"id\" => 1\n            \"nom\" => \"Partiel 1\"\n          ]\n          #original: array:2 [\n            \"id\" => 1\n            \"nom\" => \"Partiel 1\"\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        1 => App\\Models\\TypeNote {#1809\n          #connection: \"mysql\"\n          #table: \"type_notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [\n            \"id\" => 2\n            \"nom\" => \"Partiel 2\"\n          ]\n          #original: array:2 [\n            \"id\" => 2\n            \"nom\" => \"Partiel 2\"\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        2 => App\\Models\\TypeNote {#1862\n          #connection: \"mysql\"\n          #table: \"type_notes\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:2 [\n            \"id\" => 3\n            \"nom\" => \"Examen\"\n          ]\n          #original: array:2 [\n            \"id\" => 3\n            \"nom\" => \"Examen\"\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: []\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"current_semestre\" => App\\Models\\Semestre {#1866\n      #connection: \"mysql\"\n      #table: \"semestres\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:4 [\n        \"id\" => 1\n        \"nom\" => \"Semestre 1\"\n        \"niveau_id\" => 1\n        \"deleted_at\" => null\n      ]\n      #original: array:4 [\n        \"id\" => 1\n        \"nom\" => \"Semestre 1\"\n        \"niveau_id\" => 1\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: false\n      #hidden: []\n      #visible: []\n      #fillable: array:2 [\n        0 => \"nom\"\n        1 => \"niveau_id\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"current_niveau\" => App\\Models\\Niveau {#1875\n      #connection: \"mysql\"\n      #table: \"niveaux\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:4 [\n        \"id\" => 1\n        \"nom\" => \"1ère année\"\n        \"sigle\" => \"L1\"\n        \"deleted_at\" => null\n      ]\n      #original: array:4 [\n        \"id\" => 1\n        \"nom\" => \"1ère année\"\n        \"sigle\" => \"L1\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"current_annee\" => App\\Models\\AnneeUniversitaire {#1884\n      #connection: \"mysql\"\n      #table: \"annee_universitaires\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:3 [\n        \"id\" => 4\n        \"nom\" => \"2022/2023\"\n        \"deleted_at\" => null\n      ]\n      #original: array:3 [\n        \"id\" => 4\n        \"nom\" => \"2022/2023\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: false\n      #hidden: []\n      #visible: []\n      #fillable: array:1 [\n        0 => \"nom\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"current_parcour\" => App\\Models\\Parcour {#1893\n      #connection: \"mysql\"\n      #table: \"parcours\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 5\n        \"sigle\" => \"TD\"\n        \"nom\" => \"Transit et Douane\"\n        \"mention_id\" => 2\n        \"created_at\" => null\n        \"updated_at\" => null\n        \"deleted_at\" => null\n      ]\n      #original: array:7 [\n        \"id\" => 5\n        \"sigle\" => \"TD\"\n        \"nom\" => \"Transit et Douane\"\n        \"mention_id\" => 2\n        \"created_at\" => null\n        \"updated_at\" => null\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:3 [\n        0 => \"nom\"\n        1 => \"sigle\"\n        2 => \"mention_id\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"selectedSemestreId\" => \"\"\n    \"viewMode\" => \"cards\"\n    \"searchQuery\" => \"\"\n    \"sortBy\" => \"created_at_desc\"\n    \"selectedNoteType\" => 1\n    \"autoSave\" => true\n    \"saveStatus\" => []\n    \"notesStatistics\" => array:5 [\n      \"average\" => 11.609444444444\n      \"best\" => 17.22\n      \"worst\" => 0\n      \"count\" => 18\n      \"success_rate\" => 77.777777777778\n    ]\n    \"validationErrors\" => []\n    \"isValidating\" => false\n    \"page\" => \"70\"\n    \"paginators\" => array:1 [\n      \"page\" => \"70\"\n    ]\n  ]\n  \"name\" => \"etudiant\"\n  \"view\" => \"livewire.deraq.etudiant.index\"\n  \"component\" => \"App\\Http\\Livewire\\Etudiant\"\n  \"id\" => \"hoJJz2TNBUmyOStN1MHq\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/gestions/etudiants?page=70\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1751977751\n]"}, "request": {"path_info": "/livewire/message/etudiant", "status_code": "<pre class=sf-dump id=sf-dump-608835146 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-608835146\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-714955978 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-714955978\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1735860629 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">hoJJz2TNBUmyOStN1MHq</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">etudiant</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"18 characters\">gestions/etudiants</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">3eb35b33</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:54</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>showCreateModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showEditModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showDeleteModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showRestoreModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>viewSupprimesMode</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>selectedEtudiantId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>selectedUserId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>etuName</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>newUser</span>\" => []\n      \"<span class=sf-dump-key>editUser</span>\" => []\n      \"<span class=sf-dump-key>editParcours</span>\" => []\n      \"<span class=sf-dump-key>query</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filtreParcours</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filtreNiveau</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>filtreAnnee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>perPage</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>isLoading</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>sortField</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>sortDirection</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n      \"<span class=sf-dump-key>showFilters</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>compactView</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>bulkAction</span>\" => \"\"\n      \"<span class=sf-dump-key>totalEtudiants</span>\" => <span class=sf-dump-num>720</span>\n      \"<span class=sf-dump-key>totalSupprim&#233;s</span>\" => <span class=sf-dump-num>102</span>\n      \"<span class=sf-dump-key>selectedItems</span>\" => []\n      \"<span class=sf-dump-key>showNotes</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>selectedStudentName</span>\" => \"<span class=sf-dump-str title=\"16 characters\">BEHAVANA Gauffit</span>\"\n      \"<span class=sf-dump-key>noteUserId</span>\" => <span class=sf-dump-num>109</span>\n      \"<span class=sf-dump-key>noteParcourId</span>\" => <span class=sf-dump-num>5</span>\n      \"<span class=sf-dump-key>noteMatiereId</span>\" => <span class=sf-dump-num>33</span>\n      \"<span class=sf-dump-key>noteTypeId</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>noteValeur</span>\" => <span class=sf-dump-num>4</span>\n      \"<span class=sf-dump-key>noteObservation</span>\" => \"\"\n      \"<span class=sf-dump-key>noteActionMode</span>\" => \"<span class=sf-dump-str title=\"4 characters\">edit</span>\"\n      \"<span class=sf-dump-key>selectedNoteId</span>\" => <span class=sf-dump-num>10139</span>\n      \"<span class=sf-dump-key>notes</span>\" => []\n      \"<span class=sf-dump-key>matieres</span>\" => []\n      \"<span class=sf-dump-key>noteTypes</span>\" => []\n      \"<span class=sf-dump-key>current_semestre</span>\" => []\n      \"<span class=sf-dump-key>current_niveau</span>\" => []\n      \"<span class=sf-dump-key>current_annee</span>\" => []\n      \"<span class=sf-dump-key>current_parcour</span>\" => []\n      \"<span class=sf-dump-key>selectedSemestreId</span>\" => \"\"\n      \"<span class=sf-dump-key>viewMode</span>\" => \"<span class=sf-dump-str title=\"5 characters\">cards</span>\"\n      \"<span class=sf-dump-key>searchQuery</span>\" => \"\"\n      \"<span class=sf-dump-key>sortBy</span>\" => \"<span class=sf-dump-str title=\"15 characters\">created_at_desc</span>\"\n      \"<span class=sf-dump-key>selectedNoteType</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>autoSave</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>saveStatus</span>\" => []\n      \"<span class=sf-dump-key>notesStatistics</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>average</span>\" => <span class=sf-dump-num>11.609444444444</span>\n        \"<span class=sf-dump-key>best</span>\" => <span class=sf-dump-num>17.22</span>\n        \"<span class=sf-dump-key>worst</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>count</span>\" => <span class=sf-dump-num>18</span>\n        \"<span class=sf-dump-key>success_rate</span>\" => <span class=sf-dump-num>77.777777777778</span>\n      </samp>]\n      \"<span class=sf-dump-key>validationErrors</span>\" => []\n      \"<span class=sf-dump-key>isValidating</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">70</span>\"\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">70</span>\"\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>modelCollections</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>notes</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Note</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-note>array:18</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10139</span>\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-num>9979</span>\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-num>9859</span>\n            <span class=sf-dump-index>3</span> => <span class=sf-dump-num>9819</span>\n            <span class=sf-dump-index>4</span> => <span class=sf-dump-num>9699</span>\n            <span class=sf-dump-index>5</span> => <span class=sf-dump-num>8480</span>\n            <span class=sf-dump-index>6</span> => <span class=sf-dump-num>8415</span>\n            <span class=sf-dump-index>7</span> => <span class=sf-dump-num>8302</span>\n            <span class=sf-dump-index>8</span> => <span class=sf-dump-num>4461</span>\n            <span class=sf-dump-index>9</span> => <span class=sf-dump-num>4180</span>\n            <span class=sf-dump-index>10</span> => <span class=sf-dump-num>2677</span>\n            <span class=sf-dump-index>11</span> => <span class=sf-dump-num>1625</span>\n            <span class=sf-dump-index>12</span> => <span class=sf-dump-num>1511</span>\n            <span class=sf-dump-index>13</span> => <span class=sf-dump-num>1397</span>\n            <span class=sf-dump-index>14</span> => <span class=sf-dump-num>1295</span>\n            <span class=sf-dump-index>15</span> => <span class=sf-dump-num>1163</span>\n            <span class=sf-dump-index>16</span> => <span class=sf-dump-num>1106</span>\n            <span class=sf-dump-index>17</span> => <span class=sf-dump-num>885</span>\n          </samp>]\n          \"<span class=sf-dump-key>relations</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">matiere</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">typeNote</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>matieres</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Matiere</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-note>array:10</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>33</span>\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-num>34</span>\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-num>35</span>\n            <span class=sf-dump-index>3</span> => <span class=sf-dump-num>36</span>\n            <span class=sf-dump-index>4</span> => <span class=sf-dump-num>37</span>\n            <span class=sf-dump-index>5</span> => <span class=sf-dump-num>38</span>\n            <span class=sf-dump-index>6</span> => <span class=sf-dump-num>41</span>\n            <span class=sf-dump-index>7</span> => <span class=sf-dump-num>42</span>\n            <span class=sf-dump-index>8</span> => <span class=sf-dump-num>43</span>\n            <span class=sf-dump-index>9</span> => <span class=sf-dump-num>45</span>\n          </samp>]\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>noteTypes</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"19 characters\">App\\Models\\TypeNote</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>1</span>\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-num>2</span>\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-num>3</span>\n          </samp>]\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>models</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>current_semestre</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"19 characters\">App\\Models\\Semestre</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>current_niveau</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Niveau</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>current_annee</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"29 characters\">App\\Models\\AnneeUniversitaire</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>current_parcour</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Parcour</span>\"\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n          \"<span class=sf-dump-key>relations</span>\" => []\n          \"<span class=sf-dump-key>connection</span>\" => \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          \"<span class=sf-dump-key>collectionClass</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">16c0a753ae86797027c7dd7f707bd4c97cf54d65a277afbaa69aa19c2e793cdc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"5 characters\">y83oh</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">backToList</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1735860629\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-57187201 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2536</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/gestions/etudiants?page=70</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjJra0w3dW5idFI4dFlwRHFUMWdteVE9PSIsInZhbHVlIjoiL3dmcEtiSVl5MnFHREVIMkM3MVJnUnI5SVJEeVYydlh1L2wxK0J5Yk1VMUg3QWl6TmpheFY0WFRsazJWbk5QNERnNlJDK0VGRmtMVTNONWdsOFpZeWtWOUtpbTcxU0ZreTBGVE05YzRqaFBYRTlZWUFJc3pZbktLc29Nc1lDclkiLCJtYWMiOiIzMTdiMmU0YzMzMWE1ZTEzYTRiMTE5MTQ4MjUzMmIxNmViYmVmMmFmZmMyNmJiNmZiNGI4Nzk5NDE3YzlmOGFhIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6Ikg3QmlPMFpDRFprM1Mvckc4YTZYVGc9PSIsInZhbHVlIjoiRkd2S1l4RVdpRmVDbzNGRFcvVm51QTY1Z0I0L1orbTdRR1o3UEdvZ0x4eFA4dVlyWFN5QUJ1NGM0VThJSThNV1pZV1FBaUZtb045NDF0a3lKQW15bnpHczEwbVFOdmlhVmd1SVdIcVpSeFdrb1pWWE93RUhuTEhmcFlLbURwa1oiLCJtYWMiOiIwOTJmZTg3MDYzMGU1MmEwYTAyYWE5NTgyMWRjYjIwOWRhMjZmNjc3MzRiOTIyY2Y3NDdiZTFhY2VlYjY5OWJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-57187201\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1115101385 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52386</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/etudiant</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/livewire/message/etudiant</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"36 characters\">/index.php/livewire/message/etudiant</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2536</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2536</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/gestions/etudiants?page=70</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjJra0w3dW5idFI4dFlwRHFUMWdteVE9PSIsInZhbHVlIjoiL3dmcEtiSVl5MnFHREVIMkM3MVJnUnI5SVJEeVYydlh1L2wxK0J5Yk1VMUg3QWl6TmpheFY0WFRsazJWbk5QNERnNlJDK0VGRmtMVTNONWdsOFpZeWtWOUtpbTcxU0ZreTBGVE05YzRqaFBYRTlZWUFJc3pZbktLc29Nc1lDclkiLCJtYWMiOiIzMTdiMmU0YzMzMWE1ZTEzYTRiMTE5MTQ4MjUzMmIxNmViYmVmMmFmZmMyNmJiNmZiNGI4Nzk5NDE3YzlmOGFhIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6Ikg3QmlPMFpDRFprM1Mvckc4YTZYVGc9PSIsInZhbHVlIjoiRkd2S1l4RVdpRmVDbzNGRFcvVm51QTY1Z0I0L1orbTdRR1o3UEdvZ0x4eFA4dVlyWFN5QUJ1NGM0VThJSThNV1pZV1FBaUZtb045NDF0a3lKQW15bnpHczEwbVFOdmlhVmd1SVdIcVpSeFdrb1pWWE93RUhuTEhmcFlLbURwa1oiLCJtYWMiOiIwOTJmZTg3MDYzMGU1MmEwYTAyYWE5NTgyMWRjYjIwOWRhMjZmNjc3MzRiOTIyY2Y3NDdiZTFhY2VlYjY5OWJlIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751978889.4541</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751978889</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1115101385\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1150589834 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IC6ylzyOLsmp8eqEPKRtYbNYwxNcQ3yBFxnhfM9e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1150589834\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-592896537 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 08 Jul 2025 12:48:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InU4TXRSY3dtcHlBQmdmaGZXUnlmQ3c9PSIsInZhbHVlIjoiRjlES1pNZlpheDZqRERZTWF4STAvTFVlOHZqM21MZ2R2MlVUY3Q4N1dmS1RoZVB0RE9KVG5qQWVscXViWmgwOTZObkhXVmlURElONm9BLzI1T01KcXpFaS9zN3QyOVpHWDVUbDJKRVVJajNJZC91UThoWWw4MmxUbm9Lek5ZZmwiLCJtYWMiOiIyYjRjMDk1ZmEyYTU4NjFjNDE0OGVhN2U2MjZlNjUxMjZmZDY4MDNjOGM5ZmM3YzlkMjFkMDc1M2M2MDMyODZiIiwidGFnIjoiIn0%3D; expires=Tue, 08 Jul 2025 14:48:11 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6IkxxK2s3MEQwaVd6SXA3Y3RwRFhKYkE9PSIsInZhbHVlIjoiNXIyS1c2SUxEa2diYXEwSTJFcmFqZGF6aUs2MkJ4U0RBL3g0Z0tzUklZTEZjeElocGUvalhFdUp0SUFxbEhpMkNvS1pHMVJzWlpDOWJwUS9wWFRFMkFkZTlYQVZEWEcrWDd6NVJYaVZidmdMSVhkUTNFTUJvYjl6MkFRamt1WVQiLCJtYWMiOiI4MWYxYWJkZDljODZiY2YwMjlhY2ZhODQ5NmVjN2VjNWRkOWFlNmE0YzE0MGEwMDBiZDBkZjNjY2Y4ZTUyMjgxIiwidGFnIjoiIn0%3D; expires=Tue, 08 Jul 2025 14:48:11 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InU4TXRSY3dtcHlBQmdmaGZXUnlmQ3c9PSIsInZhbHVlIjoiRjlES1pNZlpheDZqRERZTWF4STAvTFVlOHZqM21MZ2R2MlVUY3Q4N1dmS1RoZVB0RE9KVG5qQWVscXViWmgwOTZObkhXVmlURElONm9BLzI1T01KcXpFaS9zN3QyOVpHWDVUbDJKRVVJajNJZC91UThoWWw4MmxUbm9Lek5ZZmwiLCJtYWMiOiIyYjRjMDk1ZmEyYTU4NjFjNDE0OGVhN2U2MjZlNjUxMjZmZDY4MDNjOGM5ZmM3YzlkMjFkMDc1M2M2MDMyODZiIiwidGFnIjoiIn0%3D; expires=Tue, 08-Jul-2025 14:48:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6IkxxK2s3MEQwaVd6SXA3Y3RwRFhKYkE9PSIsInZhbHVlIjoiNXIyS1c2SUxEa2diYXEwSTJFcmFqZGF6aUs2MkJ4U0RBL3g0Z0tzUklZTEZjeElocGUvalhFdUp0SUFxbEhpMkNvS1pHMVJzWlpDOWJwUS9wWFRFMkFkZTlYQVZEWEcrWDd6NVJYaVZidmdMSVhkUTNFTUJvYjl6MkFRamt1WVQiLCJtYWMiOiI4MWYxYWJkZDljODZiY2YwMjlhY2ZhODQ5NmVjN2VjNWRkOWFlNmE0YzE0MGEwMDBiZDBkZjNjY2Y4ZTUyMjgxIiwidGFnIjoiIn0%3D; expires=Tue, 08-Jul-2025 14:48:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-592896537\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1908947023 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FstxZbatonAMeEi2BkrWRH47PxsKFdRUFSgj9IGr</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/gestions/etudiants?page=70</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1751977751</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1908947023\", {\"maxDepth\":0})</script>\n"}}