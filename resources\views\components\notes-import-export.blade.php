<!-- Modal d'import/export de notes -->
<div class="modal fade" id="importExportModal" tabindex="-1" aria-labelledby="importExportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-gradient-primary text-white">
                <h5 class="modal-title" id="importExportModalLabel">
                    <i class="fa fa-exchange-alt me-2"></i>
                    Import / Export des notes
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <!-- Section Export -->
                    <div class="col-lg-6">
                        <div class="card border-success h-100">
                            <div class="card-header bg-success text-white">
                                <h6 class="card-title mb-0">
                                    <i class="fa fa-download me-2"></i>
                                    Exporter les notes
                                </h6>
                            </div>
                            <div class="card-body">
                                <form id="exportForm">
                                    <div class="mb-3">
                                        <label class="form-label fw-semibold">Format d'export</label>
                                        <div class="row g-2">
                                            <div class="col-6">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="exportFormat" id="exportCSV" value="csv" checked>
                                                    <label class="form-check-label" for="exportCSV">
                                                        <i class="fa fa-file-csv me-1 text-success"></i>
                                                        CSV (Excel)
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="exportFormat" id="exportPDF" value="pdf">
                                                    <label class="form-check-label" for="exportPDF">
                                                        <i class="fa fa-file-pdf me-1 text-danger"></i>
                                                        PDF
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label fw-semibold">Données à inclure</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="includeStats" checked>
                                            <label class="form-check-label" for="includeStats">
                                                Statistiques (moyennes, totaux)
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="includeObservations" checked>
                                            <label class="form-check-label" for="includeObservations">
                                                Observations et commentaires
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="includeCharts">
                                            <label class="form-check-label" for="includeCharts">
                                                Graphiques (PDF uniquement)
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label fw-semibold">Période</label>
                                        <div class="row g-2">
                                            <div class="col-6">
                                                <input type="date" class="form-control" id="exportDateFrom" placeholder="Date de début">
                                            </div>
                                            <div class="col-6">
                                                <input type="date" class="form-control" id="exportDateTo" placeholder="Date de fin">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-grid">
                                        <button type="button" class="btn btn-success" onclick="exportNotes()">
                                            <i class="fa fa-download me-2"></i>
                                            Télécharger l'export
                                        </button>
                                    </div>
                                </form>

                                <!-- Aperçu de l'export -->
                                <div class="mt-4">
                                    <h6 class="fw-semibold">Aperçu des données</h6>
                                    <div class="table-responsive" style="max-height: 200px;">
                                        <table class="table table-sm table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Matière</th>
                                                    <th>Type</th>
                                                    <th>Note</th>
                                                    <th>Date</th>
                                                </tr>
                                            </thead>
                                            <tbody id="exportPreview">
                                                <!-- Contenu généré dynamiquement -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Section Import -->
                    <div class="col-lg-6">
                        <div class="card border-primary h-100">
                            <div class="card-header bg-primary text-white">
                                <h6 class="card-title mb-0">
                                    <i class="fa fa-upload me-2"></i>
                                    Importer des notes
                                </h6>
                            </div>
                            <div class="card-body">
                                <form id="importForm" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label class="form-label fw-semibold">Fichier à importer</label>
                                        <div class="input-group">
                                            <input type="file" class="form-control" id="importFile" accept=".csv,.xlsx,.xls" onchange="previewImport(this)">
                                            <button class="btn btn-outline-secondary" type="button" onclick="downloadTemplate()">
                                                <i class="fa fa-download me-1"></i>
                                                Modèle
                                            </button>
                                        </div>
                                        <div class="form-text">
                                            <i class="fa fa-info-circle me-1"></i>
                                            Formats acceptés: CSV, Excel (.xlsx, .xls)
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label fw-semibold">Options d'import</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="skipDuplicates" checked>
                                            <label class="form-check-label" for="skipDuplicates">
                                                Ignorer les doublons
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="validateNotes" checked>
                                            <label class="form-check-label" for="validateNotes">
                                                Valider les notes (0-20)
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="createMissingSubjects">
                                            <label class="form-check-label" for="createMissingSubjects">
                                                Créer les matières manquantes
                                            </label>
                                        </div>
                                    </div>

                                    <div class="d-grid">
                                        <button type="button" class="btn btn-primary" onclick="importNotes()" disabled id="importBtn">
                                            <i class="fa fa-upload me-2"></i>
                                            Importer les notes
                                        </button>
                                    </div>
                                </form>

                                <!-- Aperçu de l'import -->
                                <div class="mt-4" id="importPreviewContainer" style="display: none;">
                                    <h6 class="fw-semibold">Aperçu du fichier</h6>
                                    <div class="alert alert-info">
                                        <div class="row">
                                            <div class="col-6">
                                                <strong>Lignes détectées:</strong> <span id="importRowCount">0</span>
                                            </div>
                                            <div class="col-6">
                                                <strong>Erreurs:</strong> <span id="importErrorCount" class="text-danger">0</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="table-responsive" style="max-height: 200px;">
                                        <table class="table table-sm table-striped">
                                            <thead id="importPreviewHeader">
                                                <!-- En-têtes générés dynamiquement -->
                                            </thead>
                                            <tbody id="importPreview">
                                                <!-- Contenu généré dynamiquement -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section des modèles -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h6 class="card-title mb-0">
                                    <i class="fa fa-templates me-2"></i>
                                    Modèles de saisie rapide
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <button class="btn btn-outline-info w-100" onclick="applyTemplate('controle')">
                                            <i class="fa fa-clipboard-check d-block mb-2 fa-2x"></i>
                                            <strong>Contrôle continu</strong>
                                            <small class="d-block text-muted">Notes de TD/TP</small>
                                        </button>
                                    </div>
                                    <div class="col-md-4">
                                        <button class="btn btn-outline-info w-100" onclick="applyTemplate('partiel')">
                                            <i class="fa fa-graduation-cap d-block mb-2 fa-2x"></i>
                                            <strong>Examen partiel</strong>
                                            <small class="d-block text-muted">Évaluations semestrielles</small>
                                        </button>
                                    </div>
                                    <div class="col-md-4">
                                        <button class="btn btn-outline-info w-100" onclick="applyTemplate('rattrapage')">
                                            <i class="fa fa-redo d-block mb-2 fa-2x"></i>
                                            <strong>Rattrapage</strong>
                                            <small class="d-block text-muted">Session de rattrapage</small>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="fa fa-times me-1"></i>Fermer
                </button>
                <button type="button" class="btn btn-info" onclick="showHelp()">
                    <i class="fa fa-question-circle me-1"></i>Aide
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card {
    transition: transform 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
}

.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

.table-responsive {
    border-radius: 0.5rem;
    border: 1px solid #dee2e6;
}

.btn-outline-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

@media (max-width: 768px) {
    .modal-xl {
        max-width: 95%;
    }
    
    .card-body {
        padding: 1rem;
    }
}
</style>

<script>
// Fonctions d'export
function exportNotes() {
    const format = document.querySelector('input[name="exportFormat"]:checked').value;
    const includeStats = document.getElementById('includeStats').checked;
    const includeObservations = document.getElementById('includeObservations').checked;
    const includeCharts = document.getElementById('includeCharts').checked;
    const dateFrom = document.getElementById('exportDateFrom').value;
    const dateTo = document.getElementById('exportDateTo').value;
    
    // Appeler la méthode Livewire pour l'export
    @this.call('exportNotesAdvanced', {
        format: format,
        includeStats: includeStats,
        includeObservations: includeObservations,
        includeCharts: includeCharts,
        dateFrom: dateFrom,
        dateTo: dateTo
    });
}

// Fonctions d'import
function previewImport(input) {
    const file = input.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = function(e) {
        const data = e.target.result;
        // Traiter le fichier CSV/Excel
        parseImportFile(data, file.name);
    };
    reader.readAsText(file);
}

function parseImportFile(data, filename) {
    // Simulation du parsing - à remplacer par une vraie logique
    const lines = data.split('\n').slice(0, 10); // Aperçu des 10 premières lignes
    
    document.getElementById('importRowCount').textContent = lines.length - 1;
    document.getElementById('importErrorCount').textContent = '0';
    document.getElementById('importPreviewContainer').style.display = 'block';
    document.getElementById('importBtn').disabled = false;
    
    // Afficher l'aperçu
    const preview = document.getElementById('importPreview');
    preview.innerHTML = lines.slice(1).map(line => {
        const cols = line.split(',');
        return `<tr>${cols.map(col => `<td>${col}</td>`).join('')}</tr>`;
    }).join('');
}

function importNotes() {
    const file = document.getElementById('importFile').files[0];
    if (!file) return;
    
    const formData = new FormData();
    formData.append('file', file);
    formData.append('skipDuplicates', document.getElementById('skipDuplicates').checked);
    formData.append('validateNotes', document.getElementById('validateNotes').checked);
    formData.append('createMissingSubjects', document.getElementById('createMissingSubjects').checked);
    
    // Appeler la méthode Livewire pour l'import
    @this.call('importNotes', formData);
}

function downloadTemplate() {
    // Télécharger un modèle CSV
    const csvContent = "Matière,Type de note,Note,Observation\nMathématiques,Contrôle continu,15.5,Bon travail\nPhysique,Examen,12.0,Peut mieux faire";
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'modele_import_notes.csv';
    a.click();
    window.URL.revokeObjectURL(url);
}

function applyTemplate(type) {
    // Fermer le modal et appliquer le modèle
    const modal = bootstrap.Modal.getInstance(document.getElementById('importExportModal'));
    modal.hide();
    
    @this.call('applyNoteTemplate', type);
}

function showHelp() {
    showToast('info', 'Consultez la documentation pour plus d\'informations sur l\'import/export des notes.');
}
</script>
