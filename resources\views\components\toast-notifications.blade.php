<!-- Toast Notifications Container -->
<div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;">
    <!-- Success Toast Template -->
    <div id="successToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                <i class="fa fa-check-circle me-2"></i>
                <span id="successMessage"></span>
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>

    <!-- Error Toast Template -->
    <div id="errorToast" class="toast align-items-center text-white bg-danger border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                <i class="fa fa-exclamation-triangle me-2"></i>
                <span id="errorMessage"></span>
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>

    <!-- Info Toast Template -->
    <div id="infoToast" class="toast align-items-center text-white bg-info border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                <i class="fa fa-info-circle me-2"></i>
                <span id="infoMessage"></span>
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>

    <!-- Warning Toast Template -->
    <div id="warningToast" class="toast align-items-center text-dark bg-warning border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                <i class="fa fa-exclamation-circle me-2"></i>
                <span id="warningMessage"></span>
            </div>
            <button type="button" class="btn-close me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>
</div>

<style>
.toast-container {
    max-width: 400px;
}

.toast {
    min-width: 300px;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
}

.toast-body {
    padding: 1rem;
    font-weight: 500;
}

.toast .btn-close {
    padding: 0.5rem;
}

/* Animation améliorée */
.toast.showing {
    animation: slideInRight 0.3s ease-out;
}

.toast.hide {
    animation: slideOutRight 0.3s ease-in;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Responsive */
@media (max-width: 576px) {
    .toast-container {
        position: fixed !important;
        top: auto !important;
        bottom: 20px !important;
        left: 20px !important;
        right: 20px !important;
        max-width: none;
    }
    
    .toast {
        min-width: auto;
        width: 100%;
    }
}
</style>

<script>
// Système de notifications amélioré
window.showToast = function(type, message, duration = 5000) {
    const toastElement = document.getElementById(type + 'Toast');
    const messageElement = document.getElementById(type + 'Message');
    
    if (toastElement && messageElement) {
        messageElement.textContent = message;
        
        const toast = new bootstrap.Toast(toastElement, {
            delay: duration,
            autohide: true
        });
        
        toast.show();
        
        // Vibration pour mobile (si supporté)
        if (navigator.vibrate && type === 'error') {
            navigator.vibrate([100, 50, 100]);
        } else if (navigator.vibrate && type === 'success') {
            navigator.vibrate(100);
        }
    }
};

// Écouteurs d'événements Livewire
window.addEventListener('showSuccessMessage', event => {
    showToast('success', event.detail.message || 'Opération effectuée avec succès!');
});

window.addEventListener('showErrorMessage', event => {
    showToast('error', event.detail.message || 'Une erreur est survenue!', 7000);
});

window.addEventListener('showInfoMessage', event => {
    showToast('info', event.detail.message, 4000);
});

window.addEventListener('showWarningMessage', event => {
    showToast('warning', event.detail.message, 6000);
});

// Notification de connexion réseau
window.addEventListener('online', () => {
    showToast('success', 'Connexion rétablie', 3000);
});

window.addEventListener('offline', () => {
    showToast('warning', 'Connexion perdue - Certaines fonctionnalités peuvent être limitées', 8000);
});
</script>
